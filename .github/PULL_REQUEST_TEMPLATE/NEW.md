<!--
    Thanks for contributing to Swift Collections!

    Before you submit your request, please replace each paragraph
    below with the relevant details, and complete the steps in the
    checklist by placing an 'x' in each box:
    
    - [x] I've completed this task
    - [ ] This task isn't completed
-->

### Description
Replace this paragraph with a description of your changes and rationale. 
Provide links to an existing issue or external references/discussions, if appropriate.

### Detailed Design
Include any additional information about the design here. At minimum, describe a synopsis of any public API additions.

```swift
/// The new feature implemented by this pull request.
public struct Example: Collection {
}
```

### Documentation
How has the new feature been documented? 
Have the relevant portions of the guides in the Documentation folder been updated in addition to symbol-level documentation?

### Testing
How is the new feature tested?

### Performance
How did you verify the new feature performs as expected?

### Source Impact
What is the impact of this change on existing users of this package? Does it deprecate or remove any existing API?

### Checklist
- [ ] I've read the [Contribution Guidelines](/README.md#contributing-to-swift-collections)
- [ ] My contributions are licensed under the [Swift license](https://swift.org/LICENSE.txt).
- [ ] I've followed the coding style of the rest of the project.
- [ ] I've added tests covering all new code paths my change adds to the project (to the extent possible).
- [ ] I've added benchmarks covering new functionality (if appropriate).
- [ ] I've verified that my change does not break any existing tests or introduce unexpected benchmark regressions.
- [ ] I've updated the documentation (if appropriate).
