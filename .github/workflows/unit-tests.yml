name: Unit Tests

on:
  workflow_call:
    inputs:
      ios-version:
        required: true
        type: string
      ios-device:
        required: true
        type: string
      macos-runner:
        required: true
        type: string
      xcode-version:
        required: false
        type: string

jobs:
  unit-tests:
    name: "${{ matrix.run-config['name'] }} on ${{ inputs.macos-runner }}"
    runs-on: ${{ inputs.macos-runner }}
    strategy:
      matrix:
        run-config:
          - {
              name: "macOS",
              condition: true,
              clean-destination: "generic/platform=macOS",
              test-destination: "platform=macOS,arch=arm64",
            }
          - {
              name: "iOS",
              condition: true,
              clean-destination: "generic/platform=iOS",
              test-destination: "platform=iOS Simulator,OS=${{ inputs.ios-version }},name=${{ inputs.ios-device }}",
            }
          - {
              name: "watchOS",
              condition: "${{ inputs.macos-runner == 'macos-15' }}",
              clean-destination: "generic/platform=watchOS",
              test-destination: "platform=watchOS Simulator,name=Apple Watch Ultra 2 (49mm)",
            }
          - {
              name: "visionOS",
              condition: "${{ inputs.macos-runner == 'macos-15' }}",
              clean-destination: "generic/platform=visionOS",
              test-destination: "platform=visionOS Simulator,name=Apple Vision Pro",
            }
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: ${{ inputs.xcode-version || 'latest-stable' }}
      - name: Setup environment
        run: make setup
      - name: Setup Cache
        id: model-cache
        uses: actions/cache@v4
        with:
          path: Models
          key: ${{ runner.os }}-models
      - name: Download Models
        if: steps.model-cache.outputs.cache-hit != 'true'
        run: make download-model MODEL=tiny
      - name: Install and discover destinations
        if: ${{ matrix.run-config['condition'] == true }}
        run: |
          if [[ "${{ matrix.run-config['name'] }}" != "macOS" ]]; then
            xcodebuild -downloadPlatform ${{ matrix.run-config['name'] }}
          fi
          echo "Runtimes for testing:"
          xcrun simctl list runtimes
          echo "Destinations for testing:"
          xcodebuild test-without-building -only-testing WhisperKitTests/UnitTests -scheme whisperkit-Package -showdestinations
      - name: Boot Simulator and Wait
        if: ${{ matrix.run-config['condition'] == true }} && ${{ matrix.run-config['name'] != 'macOS' }} && ${{ inputs.macos-runner == 'macos-15' }}
        # Slower runners require some time to fully boot the simulator
        # Parse the simulator name from the destination string, boot it, and wait
        run: |
          simulator_name=$(echo '${{ matrix.run-config['test-destination'] }}' | sed -n 's/.*name=\([^,]*\).*/\1/p')
          xcrun simctl boot "$simulator_name" || true
          sleep 15
          xcrun simctl list devices
      - name: Build and Test - ${{ matrix.run-config['name'] }}
        if: ${{ matrix.run-config['condition'] == true }}
        run: |
          set -o pipefail
          xcodebuild clean build-for-testing -scheme whisperkit-Package -destination '${{ matrix.run-config['clean-destination'] }}' | xcpretty
          xcodebuild test -only-testing WhisperKitTests/UnitTests -scheme whisperkit-Package -destination '${{ matrix.run-config['test-destination'] }}'
      - name: Upload Test Results
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.run-config['name']}}-on-${{ inputs.macos-runner }}
          path: |
            ~/Library/Developer/Xcode/DerivedData/**/Logs/Test/*.xcresult
          retention-days: 5
