import SwiftUI

@main
struct Transcriptor9000App: App {
    @StateObject private var modelManager = ModelManager()
    @StateObject private var audioManager = AudioManager()
    @StateObject private var transcriptionManager = TranscriptionManager()

    enum AppMode {
        case standard, experimental1, experimental2
    }

    let mode: AppMode = .experimental1

    init() {
        print("Transcriptor9000App: initializing with mode = \(mode)")
    }

    var body: some Scene {
        WindowGroup {
            contentView(for: mode)
                .environmentObject(modelManager)
                .environmentObject(audioManager)
                .environmentObject(transcriptionManager)
                .onAppear {
                    print("Transcriptor9000App: preparing body/Scene")
                }
        }
        .windowStyle(.titleBar)
        .windowToolbarStyle(.unifiedCompact(showsTitle: false))
        .windowResizability(.contentMinSize)
    }

    private var headerView: some View {
        print("Transcriptor9000App: creating headerView")
        return VStack(spacing: 6) {
            Text("Transcriptor 9000")
                .font(.system(.largeTitle, design: .default, weight: .bold))
                .foregroundStyle(.white)

            Text("AI-Powered Audio Transcription")
                .font(.system(.subheadline, design: .default, weight: .medium))
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 18)
        .padding(.horizontal, 20)
        .frame(height: 80)
    }
}
//
extension Transcriptor9000App {

    @ViewBuilder
    private func contentView(for mode: AppMode) -> some View {
        Group {
            switch mode {
            case .standard:
                standardView
            case .experimental1:
                geometryBasedSolution
            case .experimental2:
                geometryConstrainedSolution
            }
        }
        .onAppear { print("Transcriptor9000App: contentView for mode = \(mode)") }
    }

    private var standardView: some View {
        print("Transcriptor9000App: using standardView")
        return VStack(spacing: 0) {
            headerView
                .background(Color.black)
                .shadow(color: .black.opacity(0.3), radius: 3, x: 0, y: 2)
                .zIndex(1)

            TranscriptorMainView()
        }
        .frame(minWidth: 1000, minHeight: 700)
        .ignoresSafeArea(.container, edges: .top)
    }

    private var geometryBasedSolution: some View {
        print("Transcriptor9000App: using geometryBasedSolution")
        return GeometryReader { windowGeometry in
            VStack(spacing: 0) {
                headerView
                    .background(Color.black)
                    .shadow(color: .black.opacity(0.3), radius: 3, x: 0, y: 2)
                    .zIndex(1)

                TranscriptorMainView()
                    .frame(
                        width: windowGeometry.size.width,
                        height: windowGeometry.size.height - calculateHeaderHeight()
                    )
            }
            .position(
                x: windowGeometry.size.width / 2,
                y: windowGeometry.size.height / 2
            )
            .padding(.top, 20)
        }
        .frame(minWidth: 1000, minHeight: 700)
        .ignoresSafeArea(.all)
    }

    private func calculateHeaderHeight() -> CGFloat {
        // Dynamic header height calculation
        let baseHeight: CGFloat = 60
        let verticalPadding: CGFloat = 36
        let headerHeight = baseHeight + verticalPadding
        print("Transcriptor9000App: calculated headerHeight = \(headerHeight)")
        return headerHeight
    }

    private var geometryConstrainedSolution: some View {
        print("Transcriptor9000App: using geometryConstrainedSolution")
        return GeometryReader { geometry in
            VStack(spacing: 0) {
                headerView
                    .frame(
                        maxWidth: .infinity,
                        maxHeight: calculateHeaderHeight(for: geometry)
                    )
                    .background(Color.black)
                    .shadow(color: .black.opacity(0.3), radius: 3, x: 0, y: 2)
                    .clipped()

                TranscriptorMainView()
                    .frame(
                        maxWidth: .infinity,
                        maxHeight: geometry.size.height - calculateHeaderHeight(for: geometry)
                    )
            }
            .padding(.top, 30)
        }
        .frame(minWidth: 1000, minHeight: 700)
        .ignoresSafeArea(.container, edges: .top)
    }

    /// Calculates responsive header height based on window dimensions
    private func calculateHeaderHeight(for geometry: GeometryProxy) -> CGFloat {
        let windowHeight = geometry.size.height
        let minHeaderHeight: CGFloat = 80
        let maxHeaderHeight: CGFloat = 120

        // Responsive scaling: 8-12% of window height with bounds
        let proportionalHeight = windowHeight * 0.10
        let headerHeight = min(max(proportionalHeight, minHeaderHeight), maxHeaderHeight)
        print("Transcriptor9000App: calculateHeaderHeight(geometry) = \(headerHeight)")
        return headerHeight
    }
}
