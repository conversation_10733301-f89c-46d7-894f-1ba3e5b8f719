# ``Collections/BitSet``

<!-- DO NOT EDIT THIS FILE; IT'S AUTOMATICALLY GENERATED -->


<!-- Summary -->

<!-- ## Overview -->

## Topics

### Creating a Bit Set

- ``init()``
- ``init(reservingCapacity:)``
- ``init(_:)-15cws``
- ``init(_:)-38hho``
- ``init(_:)-2of3i``
- ``init(_:)-5fhls``
- ``init(bitPattern:)``
- ``init(words:)``
- ``random(upTo:)``
- ``random(upTo:using:)``

### Finding Elements

- ``contains(_:)``
- ``firstIndex(of:)``
- ``lastIndex(of:)``

### Adding and Updating Elements

- ``insert(_:)``
- ``update(with:)``

### Removing Elements

- ``filter(_:)``
- ``remove(_:)``
- ``remove(at:)``

### Sorted Set Operations

- ``subscript(member:)``
- ``subscript(members:)-5nkxk``
- ``subscript(members:)-5xfq5``
- ``min()``
- ``max()``
- ``sorted()``

### Combining Sets

- ``intersection(_:)-84q4u``
- ``intersection(_:)-8hcl9``
- ``intersection(_:)-7l8p3``
- ``intersection(_:)-7kgi``

- ``union(_:)-5kqmx``
- ``union(_:)-6mj8``
- ``union(_:)-50wc4``
- ``union(_:)-10had``

- ``subtracting(_:)-79e0o``
- ``subtracting(_:)-7re82``
- ``subtracting(_:)-7rn26``
- ``subtracting(_:)-42s7d``

- ``symmetricDifference(_:)-55kqn``
- ``symmetricDifference(_:)-5xt65``
- ``symmetricDifference(_:)-91kh8``
- ``symmetricDifference(_:)-79wfx``

- ``formIntersection(_:)-u07v``
- ``formIntersection(_:)-87gjl``
- ``formIntersection(_:)-9gffv``
- ``formIntersection(_:)-8t2je``

- ``formUnion(_:)-72o7q``
- ``formUnion(_:)-370hb``
- ``formUnion(_:)-7tw8j``
- ``formUnion(_:)-12ll3``

- ``subtract(_:)-9aabm``
- ``subtract(_:)-1o083``
- ``subtract(_:)-6kijg``
- ``subtract(_:)-3pynh``

- ``formSymmetricDifference(_:)-2le2k``
- ``formSymmetricDifference(_:)-5edyr``
- ``formSymmetricDifference(_:)-7wole``
- ``formSymmetricDifference(_:)-8vcnf``

### Comparing Sets

- ``==(_:_:)``
- ``isEqualSet(to:)-4xfa9``
- ``isEqualSet(to:)-359ao``
- ``isEqualSet(to:)-5ap6y``
- ``isEqualSet(to:)-2dezf``

- ``isSubset(of:)-73apg``
- ``isSubset(of:)-14xt1``
- ``isSubset(of:)-4mj71``
- ``isSubset(of:)-20wxs``

- ``isSuperset(of:)-1mfg2``
- ``isSuperset(of:)-5adir``
- ``isSuperset(of:)-4y68t``
- ``isSuperset(of:)-2m7mj``

- ``isStrictSubset(of:)-8m1z6``
- ``isStrictSubset(of:)-3y2l1``
- ``isStrictSubset(of:)-97rky``
- ``isStrictSubset(of:)-p3zj``

- ``isStrictSuperset(of:)-6e5gm``
- ``isStrictSuperset(of:)-735zn``
- ``isStrictSuperset(of:)-26acy``
- ``isStrictSuperset(of:)-5jmxx``

- ``isDisjoint(with:)-2cdg6``
- ``isDisjoint(with:)-3klxy``
- ``isDisjoint(with:)-4uidy``
- ``isDisjoint(with:)-78a8w``

### Memory Management

- ``reserveCapacity(_:)``

### Collection Views

- ``Counted-swift.struct``
- ``counted-swift.property``
