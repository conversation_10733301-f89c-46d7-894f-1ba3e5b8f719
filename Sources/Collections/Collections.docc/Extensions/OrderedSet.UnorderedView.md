# ``Collections/OrderedSet/UnorderedView``

<!-- DO NOT EDIT THIS FILE; IT'S AUTOMATICALLY GENERATED -->


<!-- Summary -->

<!-- ## Overview -->

## Topics

### Binary Set Operations

- ``intersection(_:)-3q45l``
- ``intersection(_:)-6ee3o``

- ``union(_:)-79uk3``
- ``union(_:)-23dm1``

- ``subtracting(_:)-3ct1b``
- ``subtracting(_:)-8e6mw``

- ``symmetricDifference(_:)-6aed7``
- ``symmetricDifference(_:)-7r79p``

- ``formIntersection(_:)-4ow38``
- ``formIntersection(_:)-80iht``

- ``formUnion(_:)-6ijb``
- ``formUnion(_:)-8tuol``

- ``subtract(_:)-627eq``
- ``subtract(_:)-4pjhu``

- ``formSymmetricDifference(_:)-8pkt5``
- ``formSymmetricDifference(_:)-75z52``

### Binary Set Predicates

- ``==(_:_:)`` 
- ``isEqualSet(to:)-1szq``
- ``isEqualSet(to:)-9djqq``

- ``isSubset(of:)-2dx31`` 
- ``isSubset(of:)-801lo`` 
- ``isSubset(of:)-952h5`` 

- ``isSuperset(of:)-9t33p`` 
- ``isSuperset(of:)-2vtig`` 
- ``isSuperset(of:)-9krpz`` 

- ``isStrictSubset(of:)-9o6mg``
- ``isStrictSubset(of:)-91par``
- ``isStrictSubset(of:)-7n66e`` 

- ``isStrictSuperset(of:)-89ig3``
- ``isStrictSuperset(of:)-1e0xt`` 
- ``isStrictSuperset(of:)-5dsfd`` 

- ``isDisjoint(with:)-3wuso``
- ``isDisjoint(with:)-25vmx``
- ``isDisjoint(with:)-8nfqs`` 
