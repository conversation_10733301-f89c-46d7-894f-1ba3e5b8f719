# ``Collections/OrderedSet``

<!-- DO NOT EDIT THIS FILE; IT'S AUTOMATICALLY GENERATED -->


<!-- Summary -->

<!-- ## Overview -->

## Topics

### Creating a Set

- ``init()``
- ``init(_:)-5zktd``
- ``init(_:)-3d7qr``
- ``init(_:)-68j7``
- ``init(_:)-8zm9d``
- ``init(_:)-7rt2h``
- ``init(_:)-8tli8``
- ``init(_:)-2d3a9``
- ``init(uncheckedUniqueElements:)``
- ``init(minimumCapacity:persistent:)``

### Collection Views

- ``UnorderedView``
- ``unordered``
- ``elements``

### Finding Elements

- ``contains(_:)``
- ``firstIndex(of:)``
- ``lastIndex(of:)``

### Adding and Updating Elements

- ``append(_:)``
- ``append(contentsOf:)``
- ``insert(_:at:)``
- ``updateOrAppend(_:)``
- ``updateOrInsert(_:at:)``
- ``update(_:at:)``

### Removing Elements

- ``filter(_:)``
- ``removeAll(where:)``
- ``remove(_:)``
- ``remove(at:)``
- ``removeAll(keepingCapacity:)``
- ``removeFirst()``
- ``removeLast()``
- ``removeFirst(_:)``
- ``removeLast(_:)``
- ``removeSubrange(_:)-62u6a``
- ``removeSubrange(_:)-2fqke``

### Combining Sets

- ``intersection(_:)-4o09a``
- ``intersection(_:)-9yzg3``
- ``intersection(_:)-80md4``

- ``union(_:)-67y2h``
- ``union(_:)-3lt5i``
- ``union(_:)-2939h``

- ``subtracting(_:)-5graf``
- ``subtracting(_:)-7kl8r``
- ``subtracting(_:)-1gl4y``

- ``symmetricDifference(_:)-1810l``
- ``symmetricDifference(_:)-8dvm6``
- ``symmetricDifference(_:)-9huk7``

- ``formIntersection(_:)-43o1u``
- ``formIntersection(_:)-2a4y4``
- ``formIntersection(_:)-7odn2``

- ``formUnion(_:)-6pksr``
- ``formUnion(_:)-3dkzw``
- ``formUnion(_:)-59end``

- ``subtract(_:)-3b6nj``
- ``subtract(_:)-9rtmd``
- ``subtract(_:)-9wmg8``

- ``formSymmetricDifference(_:)-96csi``
- ``formSymmetricDifference(_:)-2ll2z``
- ``formSymmetricDifference(_:)-391sm``

### Comparing Sets

- ``==(_:_:)`` 
- ``isEqualSet(to:)-6zqj7`` 
- ``isEqualSet(to:)-34yz0`` 
- ``isEqualSet(to:)-2bhxr`` 

- ``isSubset(of:)-ptij`` 
- ``isSubset(of:)-3mw6r`` 
- ``isSubset(of:)-8yb29`` 
- ``isSubset(of:)-9hxl4`` 

- ``isSuperset(of:)-4rrsh`` 
- ``isSuperset(of:)-2bbv8`` 
- ``isSuperset(of:)-7xvog`` 
- ``isSuperset(of:)-7oow7`` 

- ``isStrictSubset(of:)-8m21h``
- ``isStrictSubset(of:)-9lv3x``
- ``isStrictSubset(of:)-4efhn``
- ``isStrictSubset(of:)-10abw`` 

- ``isStrictSuperset(of:)-7u97x`` 
- ``isStrictSuperset(of:)-3kfwa``
- ``isStrictSuperset(of:)-98d9s`` 
- ``isStrictSuperset(of:)-5e6d5`` 

- ``isDisjoint(with:)-6vmoh``
- ``isDisjoint(with:)-4tsmx``
- ``isDisjoint(with:)-54iy6``
- ``isDisjoint(with:)-7nqur`` 

### Reordering Elements

- ``swapAt(_:_:)``
- ``sort()``
- ``sort(by:)``
- ``reverse()``
- ``shuffle()``
- ``shuffle(using:)``
- ``partition(by:)``

### Creating and Applying Differences

- ``difference(from:)-30bkk``
- ``applying(_:)``

### Memory Management

- ``reserveCapacity(_:)``
