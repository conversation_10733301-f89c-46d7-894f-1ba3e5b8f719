//===----------------------------------------------------------------------===//
//
// This source file is part of the Swift Collections open source project
//
// Copyright (c) 2022 - 2024 Apple Inc. and the Swift project authors
// Licensed under Apache License v2.0 with Runtime Library Exception
//
// See https://swift.org/LICENSE.txt for license information
//
//===----------------------------------------------------------------------===//


// #############################################################################
// #                                                                           #
// #            DO NOT EDIT THIS FILE; IT IS AUTOGENERATED.                    #
// #                                                                           #
// #############################################################################



// In single module mode, we need these declarations to be internal,
// but in regular builds we want them to be public. Unfortunately
// the current best way to do this is to duplicate all definitions.
#if COLLECTIONS_SINGLE_MODULE
extension UInt {
  @inlinable @inline(__always)
  internal var _firstSetBit: UInt? {
    guard self != 0 else { return nil }
    let v = UInt.bitWidth &- 1 &- self.leadingZeroBitCount
    return UInt(truncatingIfNeeded: v)
  }

  @inlinable @inline(__always)
  internal var _lastSetBit: UInt? {
    guard self != 0 else { return nil }
    return UInt(truncatingIfNeeded: self.trailingZeroBitCount)
  }
}
#else // !COLLECTIONS_SINGLE_MODULE
extension UInt {
  @inlinable @inline(__always)
  public var _firstSetBit: UInt? {
    guard self != 0 else { return nil }
    let v = UInt.bitWidth &- 1 &- self.leadingZeroBitCount
    return UInt(truncatingIfNeeded: v)
  }

  @inlinable @inline(__always)
  public var _lastSetBit: UInt? {
    guard self != 0 else { return nil }
    return UInt(truncatingIfNeeded: self.trailingZeroBitCount)
  }
}
#endif // COLLECTIONS_SINGLE_MODULE
