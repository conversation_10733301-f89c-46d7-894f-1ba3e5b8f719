//===----------------------------------------------------------------------===//
//
// This source file is part of the Swift Collections open source project
//
// Copyright (c) 2021 - 2024 Apple Inc. and the Swift project authors
// Licensed under Apache License v2.0 with Runtime Library Exception
//
// See https://swift.org/LICENSE.txt for license information
//
//===----------------------------------------------------------------------===//

%{
  from gyb_utils import *
}%
${autogenerated_warning()}

% for modifier in visibility_levels:
${visibility_boilerplate(modifier)}
extension RandomAccessCollection {
  @_alwaysEmitIntoClient @inline(__always)
  ${modifier} func _index(at offset: Int) -> Index {
    index(startIndex, offsetBy: offset)
  }

  @_alwaysEmitIntoClient @inline(__always)
  ${modifier} func _offset(of index: Index) -> Int {
    distance(from: startIndex, to: index)
  }

  @_alwaysEmitIntoClient @inline(__always)
  ${modifier} subscript(_offset offset: Int) -> Element {
    self[_index(at: offset)]
  }
}
% end
${visibility_boilerplate("end")}
