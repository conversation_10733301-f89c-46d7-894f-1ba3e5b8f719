//===----------------------------------------------------------------------===//
//
// This source file is part of the Swift Collections open source project
//
// Copyright (c) 2023 - 2024 Apple Inc. and the Swift project authors
// Licensed under Apache License v2.0 with Runtime Library Exception
//
// See https://swift.org/LICENSE.txt for license information
//
//===----------------------------------------------------------------------===//

%{
  from gyb_utils import *
}%
${autogenerated_warning()}

% for modifier in visibility_levels:
${visibility_boilerplate(modifier)}
/// Returns `x` as its concrete type `U`, or `nil` if `x` has a different
/// concrete type.
///
/// This cast can be useful for dispatching to specializations of generic
/// functions.
@_transparent
@inlinable
${modifier} func _specialize<T, U>(_ x: T, for: U.Type) -> U? {
  // Note: this was ported from recent versions of the Swift stdlib.
  guard T.self == U.self else {
    return nil
  }
  return _identityCast(x, to: U.self)
}
% end
${visibility_boilerplate("end")}
