import Foundation
import SwiftUI
@preconcurrency import WhisperKit
import CoreML

@MainActor
class ModelManager: ObservableObject {

    // MARK: - Published Properties (UI State)

    @Published var modelState: ModelState = .unloaded
    @Published var loadingProgress: Float = 0.0
    @Published var specializationProgressRatio: Float = 0.7
    @Published var loadingStepDescription: String = ""
    @Published var availableModels: [ModelInfo] = []
    @Published var isLoadingModels: Bool = false
    @Published var selectedModel: ModelInfo = ModelConfiguration.default.modelInfo
    @Published var configuration: ModelConfiguration = .default
    @Published var showError: Bool = false

    // MARK: - Internal Properties

    private var whisperKitInstance: WhisperKit?
    private var modelFolder: URL?
    private var customModelsPath: URL?
    private var loadingTask: Task<Void, Error>?

    // Default models path: ~/.whisperkit-models/models/argmaxinc/whisperkit-coreml/
    private var defaultModelsPath: URL {
        FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".whisperkit-models")
            .appendingPathComponent("models")
            .appendingPathComponent("argmaxinc")
            .appendingPathComponent("whisperkit-coreml")
    }

    // Computed properties for UI
    var isModelLoaded: Bool {
        modelState.isLoaded
    }

    var errorMessage: String? {
        modelState.errorMessage
    }

    var canTranscribe: Bool {
        modelState.isLoaded && whisperKitInstance != nil
    }
    
    // Public access to WhisperKit instance for other managers
    public var whisperKit: WhisperKit? {
        return whisperKitInstance
    }

    // MARK: - Initialization

    init() {
        print("[ModelManager] init: Initializing ModelManager")
        setupInitialState()
    }

    deinit {
        print("[ModelManager] deinit: Cleaning up ModelManager")
        loadingTask?.cancel()
    }

    // MARK: - Public Methods

    /// Main method for loading/reloading models - centralized loading logic
    func reloadModel() async {
        print("[ModelManager] reloadModel: Called for model \(selectedModel.name)")
        // Cancel any existing loading operation
        loadingTask?.cancel()

        loadingTask = Task {
            print("[ModelManager] reloadModel: Starting performModelLoading")
            try await performModelLoading()
        }

        do {
            try await loadingTask?.value
            print("[ModelManager] reloadModel: Model loaded successfully")
        } catch {
            if !Task.isCancelled {
                print("[ModelManager] reloadModel: Error occurred - \(error.localizedDescription)")
                await handleLoadingError(error)
            } else {
                print("[ModelManager] reloadModel: Task was cancelled")
            }
        }
    }

    /// Load a specific model by name
    func loadModel(_ modelInfo: ModelInfo) async {
        print("[ModelManager] loadModel: Called for model \(modelInfo.name)")
        selectedModel = modelInfo
        configuration = ModelConfiguration(
            modelInfo: modelInfo,
            computeUnits: configuration.computeUnits,
            language: configuration.language,
            enableTimestamps: configuration.enableTimestamps
        )
        await reloadModel()
    }

    /// Update model configuration and reload if needed
    func updateConfiguration(_ newConfig: ModelConfiguration) async {
        let shouldReload = configuration.modelInfo != newConfig.modelInfo ||
                          configuration.computeUnits != newConfig.computeUnits

        print("[ModelManager] updateConfiguration: Called. Should reload: \(shouldReload)")
        configuration = newConfig
        selectedModel = newConfig.modelInfo

        if shouldReload && modelState.isLoaded {
            print("[ModelManager] updateConfiguration: Reloading model due to config change")
            await reloadModel()
        }
    }

    /// Transcribe audio samples
    func transcribe(audioSamples: [Float]) async throws -> TranscriptionResult? {
        print("[ModelManager] transcribe: Called")
        guard let whisperKit = whisperKitInstance, canTranscribe else {
            print("[ModelManager] transcribe: Model not loaded, throwing error")
            throw ModelError.modelNotLoaded
        }

        return try await performTranscription(with: whisperKit, samples: audioSamples)
    }

    func cancelLoading() async {
        print("[ModelManager] cancelLoading: Called")
        loadingTask?.cancel()
        loadingTask = nil

        // Reset UI state
        modelState = .unloaded
        loadingProgress = 0.0
    }

    /// Cleanup resources
    func cleanup() async {
        print("[ModelManager] cleanup: Called")
        await cancelLoading()

        // Clean up WhisperKit resources
        whisperKitInstance = nil
        modelFolder = nil
        print("[ModelManager] cleanup: Resources cleaned up")
    }

    // MARK: - Private Methods

    private func setupInitialState() {
        print("[ModelManager] setupInitialState: Loading available models")
        // Load available models list
        loadAvailableModels()
    }

    private func loadAvailableModels() {
        print("[ModelManager] loadAvailableModels: Called")
        isLoadingModels = true
        Task {
            do {
                // Get recommended models from WhisperKit
                let recommendedModels = WhisperKit.recommendedModels()
                let remoteModels = [recommendedModels.default] + recommendedModels.disabled
                // Check for local models
                let localModels = try await getLocalModels()

                await MainActor.run {
                    var modelInfoList: [ModelInfo] = []
                    // Add all remote models, marking which ones are local
                    for modelName in remoteModels {
                        modelInfoList.append(ModelInfo(name: modelName, isLocal: false, localPath: nil))
                    }

                    // Add any local models that aren't in the remote list
                    for localModel in localModels {
                        if !remoteModels.contains(localModel.name) {
                            modelInfoList.append(localModel)
                        }
                    }

                    availableModels = modelInfoList.sorted { $0.name < $1.name }
                    isLoadingModels = false
                    print("[ModelManager] loadAvailableModels: Models loaded: \(availableModels.map { $0.name })")
                }

            } catch {
                await MainActor.run {
                    print("[ModelManager] loadAvailableModels: Error loading available models: \(error)")
                    isLoadingModels = false
                }
            }
        }
    }

    private func performModelLoading() async throws {
        print("[ModelManager] performModelLoading: Starting model loading process")
        modelState = .loading
        loadingProgress = 0.0

        do {
            // Step 1: Initialize WhisperKit (10% progress)
            loadingStepDescription = "Initializing transcription engine..."
            await updateProgress(0.1)
            try Task.checkCancellation() // Check for cancellation

            let config = WhisperKitConfig(
                computeOptions: getComputeOptions(),
                verbose: true,
                logLevel: .info,
                prewarm: false,
                load: false,
                download: false
            )

            whisperKitInstance = try await WhisperKit(config)
            guard let whisperKit = whisperKitInstance else {
                print("[ModelManager] performModelLoading: WhisperKit initialization failed")
                throw ModelError.initializationFailed
            }
            print("[ModelManager] performModelLoading: WhisperKit initialized")

            // Step 2: Download model if needed (10-60% progress)
            loadingStepDescription = "Checking for model files..."
            await updateProgress(0.2)
            try Task.checkCancellation()

            modelFolder = try await downloadModelIfNeeded(whisperKit: whisperKit)
            print("[ModelManager] performModelLoading: Model folder set to \(String(describing: modelFolder))")

            // Step 3: Load model (60-90% progress)
            loadingStepDescription = "Loading model into memory..."
            await updateProgress(0.6)
            try Task.checkCancellation()

            try await loadModelFiles(whisperKit: whisperKit)
            print("[ModelManager] performModelLoading: Model files loaded")

            // Step 4: Prewarm model (90-100% progress)
            loadingStepDescription = "Preparing model for inference..."
            await updateProgress(0.9)
            try Task.checkCancellation()

            try await prewarmModel(whisperKit: whisperKit)
            print("[ModelManager] performModelLoading: Model prewarmed")

            // Completed
            loadingStepDescription = "Model ready!"
            await updateProgress(1.0)
            modelState = .loaded
            print("[ModelManager] performModelLoading: Model loading complete")

        } catch is CancellationError {
            // Handle cancellation gracefully
            print("[ModelManager] performModelLoading: Cancelled")
            modelState = .unloaded
            loadingProgress = 0.0
            loadingStepDescription = ""
        } catch {
            print("[ModelManager] performModelLoading: Error occurred - \(error.localizedDescription)")
            throw error
        }
    }

    private func downloadModelIfNeeded(whisperKit: WhisperKit) async throws -> URL {
        print("[ModelManager] downloadModelIfNeeded: Checking for local model \(selectedModel.name)")
        // Check if model exists locally
        if let localURL = try await findLocalModel(selectedModel) {
            loadingStepDescription = "Using local model files..."
            await updateProgress(0.6) // Skip download progress

            // Update model state to show it's already downloaded
            modelState = .downloaded

            print("[ModelManager] downloadModelIfNeeded: Using local model at \(localURL)")
            return localURL
        }

        // Download model with progress tracking
        loadingStepDescription = "Downloading model files..."
        modelState = .downloading
        print("[ModelManager] downloadModelIfNeeded: Downloading model \(selectedModel.name)")

        // Create a @Sendable closure for the progress callback
        let progressCallback: @Sendable (Progress) -> Void = { [weak self] progress in
            Task { @MainActor in
                guard let self = self else { return }
                self.loadingProgress = Float(progress.fractionCompleted) * 0.4 + 0.2
                print("[ModelManager] downloadModelIfNeeded: Download progress: \(self.loadingProgress)")
            }
        }

        let modelURL = try await WhisperKit.download(
            variant: selectedModel.name,
            progressCallback: progressCallback
        )

        await MainActor.run {
            loadingProgress = 0.6
            modelState = .downloaded

            // Update the model's isLocal status in availableModels
            if let index = availableModels.firstIndex(where: { $0.name == selectedModel.name }) {
                availableModels[index] = selectedModel
            }
            print("[ModelManager] downloadModelIfNeeded: Model downloaded to \(modelURL)")
        }
        return modelURL
    }

    private func loadModelFiles(whisperKit: WhisperKit) async throws {
        print("[ModelManager] loadModelFiles: Called")
        guard let modelFolder = modelFolder else {
            print("[ModelManager] loadModelFiles: Model folder not found")
            throw ModelError.modelFolderNotFound
        }

        whisperKit.modelFolder = modelFolder
        try await whisperKit.loadModels()
        print("[ModelManager] loadModelFiles: Models loaded into WhisperKit")
    }

    private func prewarmModel(whisperKit: WhisperKit) async throws {
        print("[ModelManager] prewarmModel: Called")
        try await whisperKit.prewarmModels()
        print("[ModelManager] prewarmModel: Model prewarmed")
    }

    private func performTranscription(with whisperKit: WhisperKit, samples: [Float]) async throws -> TranscriptionResult? {
        print("[ModelManager] performTranscription: Called")
        let options = createDecodingOptions()

        // Early stopping checks
//        let decodingCallback: ((TranscriptionProgress) -> Bool?) = { (progress: TranscriptionProgress) in
//            DispatchQueue.main.async {
//                let fallbacks = Int(progress.timings.totalDecodingFallbacks)
//                let chunkId = isStreamMode ? 0 : progress.windowId
//
//                // First check if this is a new window for the same chunk, append if so
//                var updatedChunk = (chunkText: [progress.text], fallbacks: fallbacks)
//                if var currentChunk = currentChunks[chunkId], let previousChunkText = currentChunk.chunkText.last {
//                    if progress.text.count >= previousChunkText.count {
//                        // This is the same window of an existing chunk, so we just update the last value
//                        currentChunk.chunkText[currentChunk.chunkText.endIndex - 1] = progress.text
//                        updatedChunk = currentChunk
//                    } else {
//                        // This is either a new window or a fallback (only in streaming mode)
//                        if fallbacks == currentChunk.fallbacks && isStreamMode {
//                            // New window (since fallbacks havent changed)
//                            updatedChunk.chunkText = [updatedChunk.chunkText.first ?? "" + progress.text]
//                        } else {
//                            // Fallback, overwrite the previous bad text
//                            updatedChunk.chunkText[currentChunk.chunkText.endIndex - 1] = progress.text
//                            updatedChunk.fallbacks = fallbacks
//                            print("Fallback occured: \(fallbacks)")
//                        }
//                    }
//                }
//
//                // Set the new text for the chunk
//                currentChunks[chunkId] = updatedChunk
//                let joinedChunks = currentChunks.sorted { $0.key < $1.key }.flatMap { $0.value.chunkText }.joined(separator: "\n")
//
//                currentText = joinedChunks
//                currentFallbacks = fallbacks
//                currentDecodingLoops += 1
//            }
//
//            // Check early stopping
//            let currentTokens = progress.tokens
//            let checkWindow = Int(compressionCheckWindow)
//            if currentTokens.count > checkWindow {
//                let checkTokens: [Int] = currentTokens.suffix(checkWindow)
//                let compressionRatio = compressionRatio(of: checkTokens)
//                if compressionRatio > options.compressionRatioThreshold! {
//                    Logging.debug("Early stopping due to compression threshold")
//                    return false
//                }
//            }
//            if progress.avgLogprob! < options.logProbThreshold! {
//                Logging.debug("Early stopping due to logprob threshold")
//                return false
//            }
//            return nil
//        }

        let transcriptionResults: [TranscriptionResult] = try await whisperKit.transcribe(
            audioArray: samples,
            decodeOptions: options
//            callback: decodingCallback
        )

        let mergedResults = mergeTranscriptionResults(transcriptionResults)
        print("[ModelManager] performTranscription: Transcription complete")
        return mergedResults
    }
    
    private func mergeTranscriptionResults(_ results: [TranscriptionResult]) -> TranscriptionResult? {
        guard !results.isEmpty else { return nil }
        
        if results.count == 1 {
            return results.first
        }
        
        // Merge multiple results by combining segments
        let allSegments = results.flatMap { $0.segments }
        let mergedTimings = results.first?.timings ?? TranscriptionTimings()
        
        return TranscriptionResult(
            text: allSegments.map { $0.text }.joined(separator: " "),
            segments: allSegments,
            language: results.first?.language ?? "en",
            timings: mergedTimings
        )
    }

    private func createDecodingOptions() -> DecodingOptions {
        print("[ModelManager] createDecodingOptions: Called")
        return DecodingOptions(
            task: .transcribe,
            language: configuration.language,
            temperature: 0.0,
            temperatureFallbackCount: 3,
            usePrefillPrompt: true,
            usePrefillCache: true,
            skipSpecialTokens: true,
            withoutTimestamps: !configuration.enableTimestamps,
            wordTimestamps: configuration.enableTimestamps
        )
    }

    private func getComputeOptions() -> ModelComputeOptions {
        print("[ModelManager] getComputeOptions: Called")
        let mlComputeUnits: MLComputeUnits

        switch configuration.computeUnits {
        case .cpuOnly:
            mlComputeUnits = .cpuOnly
        case .cpuAndGPU:
            mlComputeUnits = .cpuAndGPU
        case .cpuAndNeuralEngine:
            mlComputeUnits = .cpuAndNeuralEngine
        }

        return ModelComputeOptions(
            audioEncoderCompute: mlComputeUnits,
            textDecoderCompute: mlComputeUnits
        )
    }

    private func findLocalModel(_ modelInfo: ModelInfo) async throws -> URL? {
        let modelPath = modelsPath.appendingPathComponent(modelInfo.name)
        print("[ModelManager] findLocalModel: Checking path \(modelPath.path)")

        if FileManager.default.fileExists(atPath: modelPath.path) {
            print("[ModelManager] findLocalModel: Found local model at \(modelPath.path)")
            return modelPath
        }

        print("[ModelManager] findLocalModel: No local model found for \(modelInfo.name)")
        return nil
    }

    private func getLocalModels() async throws -> [ModelInfo] {
        print("[ModelManager] getLocalModels: Called")
        guard FileManager.default.fileExists(atPath: modelsPath.path) else {
            print("[ModelManager] getLocalModels: No models directory at \(modelsPath.path)")
            return []
        }

        do {
            let contents = try FileManager.default.contentsOfDirectory(at: modelsPath, includingPropertiesForKeys: [.isDirectoryKey], options: [.skipsHiddenFiles])

            let localModels = contents.filter { url in
                (try? url.resourceValues(forKeys: [.isDirectoryKey]).isDirectory) == true
            }.map { ModelInfo(name: $0.lastPathComponent, isLocal: true, localPath: $0)}
            print("[ModelManager] getLocalModels: Found local models: \(localModels.map { $0.name })")
            return localModels
        } catch {
            print("[ModelManager] getLocalModels: Error enumerating local models: \(error)")
            return []
        }
    }

    private func updateProgress(_ progress: Float) async {
        loadingProgress = progress
        print("[ModelManager] updateProgress: Progress updated to \(progress)")
    }

    private func handleLoadingError(_ error: Error) async {
        let errorMessage: String

        switch error {
        case ModelError.modelNotFound:
            errorMessage = "Selected model was not found"
        case ModelError.downloadFailed:
            errorMessage = "Failed to download model"
        case ModelError.initializationFailed:
            errorMessage = "Failed to initialize WhisperKit"
        case ModelError.modelFolderNotFound:
            errorMessage = "Model folder not found"
        default:
            errorMessage = error.localizedDescription
        }

        print("[ModelManager] handleLoadingError: \(errorMessage)")
        modelState = .error(errorMessage)
        showError = true
        loadingProgress = 0.0
    }

    /// Set custom models directory path
    func setCustomModelsPath(_ path: URL) {
        print("[ModelManager] setCustomModelsPath: Setting custom path to \(path.path)")
        customModelsPath = path
        // Reload available models with new path
        loadAvailableModels()
    }

    /// Get current models path (custom or default)
    private var modelsPath: URL {
        return customModelsPath ?? defaultModelsPath
    }
}

// MARK: - Model Errors

enum ModelError: LocalizedError {
    case modelNotLoaded
    case modelNotFound
    case downloadFailed
    case initializationFailed
    case modelFolderNotFound

    var errorDescription: String? {
        switch self {
        case .modelNotLoaded:
            return "Model is not loaded. Please load a model first."
        case .modelNotFound:
            return "The selected model could not be found."
        case .downloadFailed:
            return "Failed to download the model. Please check your internet connection."
        case .initializationFailed:
            return "Failed to initialize the transcription engine."
        case .modelFolderNotFound:
            return "Model folder could not be located."
        }
    }
}
