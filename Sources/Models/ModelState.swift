import Foundation
@preconcurrency import WhisperKit

// MARK: - Model State Management

enum ModelState: Equatable {
    case downloading
    case downloaded
    case unloaded
    case loading
    case loaded
    case error(String)

    var isLoaded: Bool {
        if case .loaded = self {
            return true
        }
        return false
    }

    var isLoading: Bool {
        if case .loading = self {
            return true
        }
        return false
    }

    var isDownloading: Bool {
        if case .downloading = self {
            return true
        }
        return false
    }

    var isDownloaded: Bool {
        if case .downloaded = self {
            return true
        }
        return false
    }

    var errorMessage: String? {
        if case .error(let message) = self {
            return message
        }
        return nil
    }

    var description: String {
        switch self {
        case .downloading:
            return "Downloading model..."
        case .downloaded:
            return "Model downloaded"
        case .unloaded:
            return "Model not loaded"
        case .loading:
            return "Loading model..."
        case .loaded:
            return "Model ready"
        case .error(let message):
            return "Error: \(message)"
        }
    }
}

struct ModelInfo: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let isLocal: Bool
    let localPath: URL?

    static func == (lhs: ModelInfo, rhs: ModelInfo) -> Bool {
        lhs.name == rhs.name && lhs.isLocal == rhs.isLocal
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(name)
        hasher.combine(isLocal)
    }
}

// MARK: - Model Configuration

struct ModelConfiguration {
    let modelInfo: ModelInfo
    let computeUnits: ComputeUnits
    let language: String
    let enableTimestamps: Bool

    static let `default` = ModelConfiguration(
        modelInfo: ModelInfo(name: "openai_whisper-tiny", isLocal: true, localPath: nil),
        computeUnits: .cpuAndNeuralEngine,
        language: "en",
        enableTimestamps: true
    )
}

enum ComputeUnits: String, CaseIterable {
    case cpuOnly = "CPU Only"
    case cpuAndGPU = "CPU + GPU"
    case cpuAndNeuralEngine = "CPU + Neural Engine"

    var description: String {
        return rawValue
    }
}

// MARK: - Audio Device Models

struct AudioDevice: Identifiable, Hashable {
    let id: String
    let name: String
    let isDefault: Bool

    init(id: String, name: String, isDefault: Bool = false) {
        self.id = id
        self.name = name
        self.isDefault = isDefault
    }
}

// MARK: - Transcription Models

struct TranscriptionSegment: Identifiable, Equatable {
    let id = UUID()
    let text: String
    let start: Float
    let end: Float
    let confidence: Float?
    
    // Convert from WhisperKit's segment format
    init(from whisperSegment: any WhisperKit.TranscriptionSegment) {
        self.text = whisperSegment.text
        self.start = whisperSegment.start
        self.end = whisperSegment.end
        self.confidence = nil
    }
    
    // Direct init for testing or manual creation
    init(text: String, start: Float, end: Float, confidence: Float? = nil) {
        self.text = text
        self.start = start
        self.end = end
        self.confidence = confidence
    }
}