import Foundation
import SwiftUI

@MainActor
struct ModelStatusView: View {
    @EnvironmentObject private var modelManager: ModelManager

    var body: some View {
        let _ = print("[ModelStatusView] Body evaluated")
        ZStack(alignment: .leading) {
            VStack(spacing: 12) {
                // Status indicator + text
                HStack(alignment: .center, spacing: 8) {
                    // Picker (jeśli dostępne modele)
                    if !modelManager.availableModels.isEmpty && !modelManager.isLoadingModels {
                        VStack(alignment: .leading, spacing: 4) {
                            Picker("Model", selection: $modelManager.selectedModel) {
                                ForEach(modelManager.availableModels, id: \.self) { model in
                                    Text(model.name).tag(model.name)
                                }
                            }
                            .pickerStyle(.menu)
                            .disabled(modelManager.modelState.isLoading)
                            .onChange(of: modelManager.selectedModel) { oldValue, newValue in
                                print("[ModelStatusView] Picker changed from \(oldValue) to \(newValue)")
                                if modelManager.modelState.isLoaded {
                                    print("[ModelStatusView] Picker change triggers model load for \(newValue)")
                                    Task {
                                        await modelManager.loadModel(newValue)
                                    }
                                }
                            }
                            .frame(maxWidth: 300)
                        }
                    }

                    Image(systemName: "circle.fill")
                        .foregroundStyle(statusColor)
                        .symbolEffect(.variableColor, isActive: modelManager.modelState.isLoading)
                        .onTapGesture {
                            print("[ModelStatusView] Status indicator tapped")
                            if modelManager.modelState.isLoading || modelManager.modelState.isDownloading {
                                print("[ModelStatusView] Cancel loading requested")
                                Task {
                                    await modelManager.cancelLoading()
                                }
                            }
                        }
                        .help(modelManager.modelState.isLoading || modelManager.modelState.isDownloading ? "Tap to cancel loading" : "")
                }

                // Przycisk ładowania (jeśli wymagany)
                if modelManager.modelState == .unloaded {
                    Button("Load Selected Model") {
                        print("[ModelStatusView] Load Selected Model button tapped for \(modelManager.selectedModel)")
                        Task {
                            await modelManager.loadModel(modelManager.selectedModel)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.regular)
                    .padding(.top, 12)
                }

                // ProgressView (jeśli ładowane są modele)
                if modelManager.isLoadingModels {
                    ProgressView()
                        .progressViewStyle(.circular)
                        .scaleEffect(0.9)
                        .onAppear {
                            print("[ModelStatusView] ProgressView appeared (loading models)")
                        }
                }
            }
            .padding()
            .frame(minWidth: 600, minHeight: 120)
            .background(
                VisualEffectView(material: .contentBackground, blendingMode: .withinWindow)
                    .cornerRadius(12)
            )
            .cornerRadius(12)
            .shadow(radius: 4)
            .onAppear {
                print("[ModelStatusView] View appeared")
            }
        }
    }

    private var pickerTitle: String {
        modelManager.modelState == .unloaded ? "Select Model" : "Model"
    }

    private var statusColor: Color {
        switch modelManager.modelState {
        case .downloading:
            return .yellow
        case .downloaded:
            return .blue
        case .loaded:
            return .green
        case .loading:
            return .orange
        case .error:
            return .red
        case .unloaded:
            return .gray
        }
    }

    private var statusText: String {
        switch modelManager.modelState {
        case .downloading:
            return "Downloading Model..."
        case .downloaded:
            return "Model Downloaded"
        case .loaded:
            return "Model Ready"
        case .loading:
            return "Loading Model..."
        case .error(let message):
            return "Error: \(message)"
        case .unloaded:
            return "No Model Loaded"
        }
    }
}
