import Foundation
import SwiftUI

@MainActor
struct TranscriptorMainView: View {
    @EnvironmentObject private var modelManager: ModelManager
    @EnvironmentObject private var audioManager: AudioManager
    @EnvironmentObject private var transcriptionManager: TranscriptionManager

    @State private var selectedTab = 0

    var body: some View {
        VStack(spacing: 20) {
            mainContentView
            ModelStatusView()
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .task {
            print("[TranscriptorMainView] .task: initializeApp() called")
            await initializeApp()
        }
        .onDisappear {
            print("[TranscriptorMainView] .onDisappear: cleanupResources() called")
            cleanupResources()
        }
        .onAppear {
            print("[TranscriptorMainView] .onAppear")
        }
        .alert("Model Loading Failed", isPresented: $modelManager.showError) {
            Button("Retry") {
                print("[TranscriptorMainView] Alert: Retry tapped")
                Task { await reloadModel() }
            }
            But<PERSON>("Cancel", role: .cancel) {
                print("[TranscriptorMainView] Alert: Cancel tapped")
                modelManager.showError = false
            }
        } message: {
            Text(modelManager.errorMessage ?? "Unknown error occurred")
        }
    }

    // MARK: - View Components

    private var mainContentView: some View {
        Group {
            switch modelManager.modelState {
            case .loaded:
                loadedStateView
            case .loading:
                loadingStateView
            default:
                unloadedStateView
            }
        }
        .onAppear {
            print("[TranscriptorMainView] mainContentView: modelState = \(modelManager.modelState)")
        }
    }

    private var loadedStateView: some View {
        TabView(selection: $selectedTab) {
            RecordingView()
                .tabItem {
                    Label("Record & Transcribe", systemImage: "waveform")
                }
                .tag(0)

            LiveTranscriptionView()
                .tabItem {
                    Label("Live Transcription", systemImage: "mic.fill")
                }
                .tag(1)
        }
        .padding()
        .onAppear {
            print("[TranscriptorMainView] loadedStateView: TabView appeared, selectedTab = \(selectedTab)")
        }
        .onChange(of: selectedTab) { oldValue, newValue in
            print("[TranscriptorMainView] loadedStateView: selectedTab changed from \(oldValue) to \(newValue)")
        }
    }

    private var loadingStateView: some View {
        VStack(spacing: 16) {
            if modelManager.loadingProgress > 0 {
                // Deterministic progress with percentage
                ProgressView(value: modelManager.loadingProgress, total: 1.0)
                    .frame(maxWidth: 300)

                Text("Loading model... \(Int(modelManager.loadingProgress * 100))%")
                    .font(.headline)

                Text(modelManager.loadingStepDescription)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            } else {
                // Indeterminate spinner for initialization
                ProgressView("Initializing...")
                    .progressViewStyle(CircularProgressViewStyle(tint: .accentColor))

                Text("Preparing model for loading")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .onAppear {
            print("[TranscriptorMainView] loadingStateView: appeared, progress = \(modelManager.loadingProgress), step = \(modelManager.loadingStepDescription)")
        }
    }

    private var errorStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.red)

            Text("Model Loading Failed")
                .font(.headline)

            Text(modelManager.errorMessage ?? "Unknown error occurred")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Retry") {
                print("[TranscriptorMainView] errorStateView: Retry tapped")
                Task {
                    await reloadModel()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .onAppear {
            print("[TranscriptorMainView] errorStateView: appeared")
        }
    }

    private var unloadedStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "brain")
                .font(.system(size: 60))
                .foregroundColor(.accentColor)

            Text("Select a Model to Begin")
                .font(.title2)
                .fontWeight(.semibold)

        }
        .padding()
        .onAppear {
            print("[TranscriptorMainView] unloadedStateView: appeared")
        }
    }

    // MARK: - Private Methods

    private func initializeApp() async {
        print("[TranscriptorMainView] initializeApp: Setting up audio devices")
        // Initialize audio devices
        await audioManager.setupAudioDevices()

        // No longer auto-loading the model on startup
        // The user will need to select and load a model manually
        print("[TranscriptorMainView] initializeApp: Finished setup")
    }

    private func reloadModel() async {
        print("[TranscriptorMainView] reloadModel: Called")
        await modelManager.reloadModel()
    }

    private func cleanupResources() {
        print("[TranscriptorMainView] cleanupResources: Called")
        // Stop any ongoing operations
        Task {
            await audioManager.teardown()
            await modelManager.cleanup()
            await transcriptionManager.cleanup()
            print("[TranscriptorMainView] cleanupResources: All managers cleaned up")
        }
    }
}
