//
//  LexerTests.swift
//
//
//  Created by <PERSON> on 2024/3/20.
//

import XCTest

@testable import Jinja

final class LexerTests: XCTestCase {
    let testStrings: [String: String] = [
        // Text nodes
        "NO_TEMPLATE": "Hello world!",
        "TEXT_NODES": "0{{ 'A' }}1{{ 'B' }}{{ 'C' }}2{{ 'D' }}3",

        // Logical operators
        "LOGICAL_AND": "{{ true and true }}{{ true and false }}{{ false and true }}{{ false and false }}",
        "LOGICAL_OR": "{{ true or true }}{{ true or false }}{{ false or true }}{{ false or false }}",
        "LOGICAL_NOT": "{{ not true }}{{ not false }}",
        "LOGICAL_NOT_NOT": "{{ not not true }}{{ not not false }}",
        "LOGICAL_AND_OR":
            "{{ true and true or false }}{{ true and false or true }}{{ false and true or true }}{{ false and false or true }}{{ false and false or false }}",
        "LOGICAL_AND_NOT":
            "{{ true and not true }}{{ true and not false }}{{ false and not true }}{{ false and not false }}",
        "LOGICAL_OR_NOT":
            "{{ true or not true }}{{ true or not false }}{{ false or not true }}{{ false or not false }}",
        "LOGICAL_COMBINED": "{{ 1 == 2 and 2 == 2 }}{{ 1 == 2 or 2 == 2}}",

        // If statements
        "IF_ONLY": "{% if 1 == 1 %}{{ 'A' }}{% endif %}{{ 'B' }}",
        "IF_ELSE_ONLY": "{% if 1 == 2 %}{{ 'A' }}{% else %}{{ 'B' }}{% endif %}{{ 'C' }}",
        "IF_ELIF_ELSE":
            "{% if 1 == 2 %}{{ 'A' }}{{ 'B' }}{{ 'C' }}{% elif 1 == 2 %}{{ 'D' }}{% elif 1 == 3 %}{{ 'E' }}{{ 'F' }}{% else %}{{ 'G' }}{{ 'H' }}{{ 'I' }}{% endif %}{{ 'J' }}",
        "NESTED_STATEMENTS":
            "{% set a = 0 %}{% set b = 0 %}{% set c = 0 %}{% set d = 0 %}{% if 1 == 1 %}{% set a = 2 %}{% set b = 3 %}{% elif 1 == 2 %}{% set c = 4 %}{% else %}{% set d = 5 %}{% endif %}{{ a }}{{ b }}{{ c }}{{ d }}",

        // For loops
        "FOR_LOOP": "{% for message in messages %}{{ message['content'] }}{% endfor %}",
        "FOR_LOOP_UNPACKING": "|{% for x, y in [ [1, 2], [3, 4] ] %}|{{ x + ' ' + y }}|{% endfor %}|",

        // Set variables
        "VARIABLES": "{% set x = 'Hello' %}{% set y = 'World' %}{{ x + ' ' + y }}",

        // Numbers
        "NUMBERS": "|{{ 5 }}|{{ -5 }}|{{ add(3, -1) }}|{{ (3 - 1) + (a - 5) - (a + 5)}}|",

        // Binary expressions
        "BINOP_EXPR": "{{ 1 % 2 }}{{ 1 < 2 }}{{ 1 > 2 }}{{ 1 >= 2 }}{{ 2 <= 2 }}{{ 2 == 2 }}{{ 2 != 3 }}{{ 2 + 3 }}",

        // Strings
        "STRINGS": "{{ 'Bye' }}{{ bos_token + '[INST] ' }}",
        "STRINGS_1": "|{{ \"test\" }}|{{ \"a\" + 'b' + \"c\" }}|{{ '\"' + \"'\" }}|{{ '\\'' }}|{{ \"\\\"\" }}|",
        "STRINGS_2": "|{{ \"\" | length }}|{{ \"a\" | length }}|{{ '' | length }}|{{ 'a' | length }}|",

        // Function calls
        "FUNCTIONS": "{{ func() }}{{ func(apple) }}{{ func(x, 'test', 2, false) }}",

        // Object properties
        "PROPERTIES": "{{ obj.x + obj.y }}{{ obj['x'] + obj.y }}",

        // Object methods
        "OBJ_METHODS": "{{ obj.x(x, y) }}{{ ' ' + obj.x() + ' ' }}{{ obj.z[x](x, y) }}",
        "STRING_METHODS":
            "{{ '  A  '.strip() }}{% set x = '  B  ' %}{{ x.strip() }}{% set y = ' aBcD ' %}{{ y.upper() }}{{ y.lower() }}",
        "STRING_METHODS_2": "{{ 'test test'.title() }}",

        // String indexing and slicing
        "STRING_SLICING": "|{{ x[0] }}|{{ x[:] }}|{{ x[:3] }}|{{ x[1:4] }}|{{ x[1:-1] }}|{{ x[1::2] }}|{{ x[5::-1] }}|",

        // Array indexing and slicing
        "ARRAY_SLICING":
            "|{{ strings[0] }}|{% for s in strings[:] %}{{ s }}{% endfor %}|{% for s in strings[:3] %}{{ s }}{% endfor %}|{% for s in strings[1:4] %}{{ s }}{% endfor %}|{% for s in strings[1:-1] %}{{ s }}{% endfor %}|{% for s in strings[1::2] %}{{ s }}{% endfor %}|{% for s in strings[5::-1] %}{{ s }}{% endfor %}|",

        // Membership operators
        "MEMBERSHIP":
            "|{{ 0 in arr }}|{{ 1 in arr }}|{{ true in arr }}|{{ false in arr }}|{{ 'a' in arr }}|{{ 'b' in arr }}|",
        "MEMBERSHIP_NEGATION_1":
            "|{{ not 0 in arr }}|{{ not 1 in arr }}|{{ not true in arr }}|{{ not false in arr }}|{{ not 'a' in arr }}|{{ not 'b' in arr }}|",
        "MEMBERSHIP_NEGATION_2":
            "|{{ 0 not in arr }}|{{ 1 not in arr }}|{{ true not in arr }}|{{ false not in arr }}|{{ 'a' not in arr }}|{{ 'b' not in arr }}|",

        // Escaped characters
        "ESCAPED_CHARS": "{{ '\\n' }}{{ '\\t' }}{{ '\\'' }}{{ '\\\"' }}{{ '\\\\' }}{{ '|\\n|\\t|\\'|\\\"|\\\\|' }}",

        // Substring inclusion
        "SUBSTRING_INCLUSION":
            "|{{ '' in 'abc' }}|{{ 'a' in 'abc' }}|{{ 'd' in 'abc' }}|{{ 'ab' in 'abc' }}|{{ 'ac' in 'abc' }}|{{ 'abc' in 'abc' }}|{{ 'abcd' in 'abc' }}|",

        // Filter operator
        "FILTER_OPERATOR": "{{ arr | length }}{{ 1 + arr | length }}{{ 2 + arr | sort | length }}{{ (arr | sort)[0] }}",
        "FILTER_OPERATOR_2":
            "|{{ 'abc' | length }}|{{ 'aBcD' | upper }}|{{ 'aBcD' | lower }}|{{ 'test test' | capitalize}}|{{ 'test test' | title }}|{{ ' a b ' | trim }}|{{ '  A  B  ' | trim | lower | length }}|",
        "FILTER_OPERATOR_3": "|{{ -1 | abs }}|{{ 1 | abs }}|",
        "FILTER_OPERATOR_4": "{{ items | selectattr('key') | length }}",
        "FILTER_OPERATOR_5": "{{ messages | selectattr('role', 'equalto', 'system') | length }}",
        "FILTER_OPERATOR_6": "|{{ obj | length }}|{{ (obj | items)[1:] | length }}|",

        // Logical operators between non-Booleans
        "BOOLEAN_NUMERICAL":
            "|{{ 1 and 2 }}|{{ 1 and 0 }}|{{ 0 and 1 }}|{{ 0 and 0 }}|{{ 1 or 2 }}|{{ 1 or 0 }}|{{ 0 or 1 }}|{{ 0 or 0 }}|{{ not 1 }}|{{ not 0 }}|",
        "BOOLEAN_STRINGS":
            "|{{ 'a' and 'b' }}|{{ 'a' and '' }}|{{ '' and 'a' }}|{{ '' and '' }}|{{ 'a' or 'b' }}|{{ 'a' or '' }}|{{ '' or 'a' }}|{{ '' or '' }}|{{ not 'a' }}|{{ not '' }}|",
        "BOOLEAN_MIXED":
            "|{{ true and 1 }}|{{ true and 0 }}|{{ false and 1 }}|{{ false and 0 }}|{{ true or 1 }}|{{ true or 0 }}|{{ false or 1 }}|{{ false or 0 }}|",
        "BOOLEAN_MIXED_2":
            "|{{ true and '' }}|{{ true and 'a' }}|{{ false or '' }}|{{ false or 'a' }}|{{ '' and true }}|{{ 'a' and true }}|{{ '' or false }}|{{ 'a' or false }}|",
        "BOOLEAN_MIXED_IF":
            "{% if '' %}{{ 'A' }}{% endif %}{% if 'a' %}{{ 'B' }}{% endif %}{% if true and '' %}{{ 'C' }}{% endif %}{% if true and 'a' %}{{ 'D' }}{% endif %}",

        // Tests (is operator)
        "IS_OPERATOR":
            "|{{ unknown_var is defined }}|{{ unknown_var is not defined }}|{{ known_var is defined }}|{{ known_var is not defined }}|",
        "IS_OPERATOR_2":
            "|{{ true is true }}|{{ true is not true }}|{{ true is false }}|{{ true is not false }}|{{ true is boolean }}|{{ 1 is boolean }}|",
        "IS_OPERATOR_3":
            "|{{ 1 is odd }}|{{ 2 is odd }}|{{ 1 is even }}|{{ 2 is even }}|{{ 2 is number }}|{{ '2' is number }}|{{ 2 is integer }}|{{ '2' is integer }}|",
        "IS_OPERATOR_4": "|{{ func is callable }}|{{ 2 is callable }}|{{ 1 is iterable }}|{{ 'hello' is iterable }}|",
        "IS_OPERATOR_5": "|{{ 'a' is lower }}|{{ 'A' is lower }}|{{ 'a' is upper }}|{{ 'A' is upper }}|",

        // Short-circuit evaluation
        "SHORT_CIRCUIT": "{{ false and raise_exception('This should not be printed') }}",
        "SHORT_CIRCUIT_1": "{{ true or raise_exception('This should not be printed') }}",

        // Namespaces
        "NAMESPACE": "{% set ns = namespace() %}{% set ns.foo = 'bar' %}{{ ns.foo }}",
        "NAMESPACE_1": "{% set ns = namespace(default=false) %}{{ ns.default }}",
        "NAMESPACE_2": "{% set ns = namespace(default=false, number=1+1) %}|{{ ns.default }}|{{ ns.number }}|",

        // Object operators
        "OBJECT_OPERATORS":
            "|{{ 'known' in obj }}|{{ 'known' not in obj }}|{{ 'unknown' in obj }}|{{ 'unknown' not in obj }}|",
        "OBJECT_OPERATORS_1":
            "|{{ obj.get('known') }}|{{ obj.get('unknown') is none }}|{{ obj.get('unknown') is defined }}|",
        "OBJECT_OPERATORS_2": "|{% for x, y in obj.items() %}|{{ x + ' ' + y }}|{% endfor %}|",

        // Scope
        "SCOPE":
            "{% set ns = namespace(found=false) %}{% for num in nums %}{% if num == 1 %}{{ 'found=' }}{% set ns.found = true %}{% endif %}{% endfor %}{{ ns.found }}",
        "SCOPE_1":
            "{% set found = false %}{% for num in nums %}{% if num == 1 %}{{ 'found=' }}{% set found = true %}{% endif %}{% endfor %}{{ found }}",

        // Undefined
        "UNDEFINED_VARIABLES": "{{ undefined_variable }}",
        "UNDEFINED_ACCESS": "{{ object.undefined_attribute }}",

        // Null
        "NULL_VARIABLE":
            "{% if not null_val is defined %}{% set null_val = none %}{% endif %}{% if null_val is not none %}{{ 'fail' }}{% else %}{{ 'pass' }}{% endif %}",

        // Ternary operator
        "TERNARY_OPERATOR":
            "|{{ 'a' if true else 'b' }}|{{ 'a' if false else 'b' }}|{{ 'a' if 1 + 1 == 2 else 'b' }}|{{ 'a' if 1 + 1 == 3 or 1 * 2 == 3 else 'b' }}|",

        // Array literals
        "ARRAY_LITERALS": "{{ [1, true, 'hello', [1, 2, 3, 4], var] | length }}",

        // Tuple literals
        "TUPLE_LITERALS": "{{ (1, (1, 2)) | length }}",

        // Object literals
        "OBJECT_LITERALS": "{{ { 'key': 'value', key: 'value2', \"key3\": [1, {'foo': 'bar'} ] }['key'] }}",

        // Array operators
        "ARRAY_OPERATORS": "{{ ([1, 2, 3] + [4, 5, 6]) | length }}",
    ]

    let testParsed: [String: [Token]] = [
        "NO_TEMPLATE": [Token(value: "Hello world!", type: .text)],
        "TEXT_NODES": [
            Token(value: "0", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "1", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "B", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "C", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "2", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "D", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "3", type: .text),
        ],

        // Logical operators
        "LOGICAL_AND": [
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_OR": [
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_NOT": [
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_NOT_NOT": [
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_AND_OR": [
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_AND_NOT": [
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_OR_NOT": [
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "LOGICAL_COMBINED": [
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "and", type: .and),
            Token(value: "2", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "or", type: .or),
            Token(value: "2", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
        ],

        // If statements
        "IF_ONLY": [
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "B", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "IF_ELSE_ONLY": [
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "else", type: .else),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "B", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "C", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "IF_ELIF_ELSE": [
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "B", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "C", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "elif", type: .elseIf),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "D", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "elif", type: .elseIf),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "3", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "E", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "F", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "else", type: .else),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "G", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "H", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "I", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "J", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "NESTED_STATEMENTS": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "a", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "0", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "b", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "0", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "c", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "0", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "d", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "0", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "a", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "2", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "b", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "3", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "elif", type: .elseIf),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "c", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "4", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "else", type: .else),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "d", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "5", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "b", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "c", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "d", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // For loops
        "FOR_LOOP": [
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "message", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "messages", type: .identifier),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "message", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "content", type: .stringLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
        ],
        "FOR_LOOP_UNPACKING": [
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "x", type: .identifier),
            Token(value: ",", type: .comma),
            Token(value: "y", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "2", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: ",", type: .comma),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "3", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "4", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: " ", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "y", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
        ],

        // Set variables
        "VARIABLES": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "x", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "Hello", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "y", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "World", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: " ", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "y", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // Numbers
        "NUMBERS": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "5", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "-5", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "add", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "3", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "-1", type: .numericLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "(", type: .openParen),
            Token(value: "3", type: .numericLiteral),
            Token(value: "-", type: .additiveBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "(", type: .openParen),
            Token(value: "a", type: .identifier),
            Token(value: "-", type: .additiveBinaryOperator),
            Token(value: "5", type: .numericLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "-", type: .additiveBinaryOperator),
            Token(value: "(", type: .openParen),
            Token(value: "a", type: .identifier),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "5", type: .numericLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Binary expressions
        "BINOP_EXPR": [
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "%", type: .multiplicativeBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "<", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: ">", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: ">=", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "<=", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "!=", type: .comparisonBinaryOperator),
            Token(value: "3", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "3", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
        ],

        // Strings
        "STRINGS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "Bye", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "bos_token", type: .identifier),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "[INST] ", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
        ],
        "STRINGS_1": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "test", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "b", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "c", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "\"", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "'", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "'", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "\"", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "STRINGS_2": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Function calls
        "FUNCTIONS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "func", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "func", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "apple", type: .identifier),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "func", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "x", type: .identifier),
            Token(value: ",", type: .comma),
            Token(value: "test", type: .stringLiteral),
            Token(value: ",", type: .comma),
            Token(value: "2", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "false", type: .booleanLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
        ],

        // Object properties
        "PROPERTIES": [
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "x", type: .identifier),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "y", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "x", type: .stringLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "y", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // Object methods
        "OBJ_METHODS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "x", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "x", type: .identifier),
            Token(value: ",", type: .comma),
            Token(value: "y", type: .identifier),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: " ", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "x", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: " ", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "z", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "x", type: .identifier),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "(", type: .openParen),
            Token(value: "x", type: .identifier),
            Token(value: ",", type: .comma),
            Token(value: "y", type: .identifier),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
        ],

        // String methods
        "STRING_METHODS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "  A  ", type: .stringLiteral),
            Token(value: ".", type: .dot),
            Token(value: "strip", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "x", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "  B  ", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "strip", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "y", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: " aBcD ", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "y", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "upper", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "y", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "lower", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
        ],
        "STRING_METHODS_2": [
            Token(value: "{{", type: .openExpression),
            Token(value: "test test", type: .stringLiteral),
            Token(value: ".", type: .dot),
            Token(value: "title", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
        ],

        // String indexing and slicing
        "STRING_SLICING": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "0", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: ":", type: .colon),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: ":", type: .colon),
            Token(value: "3", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: "4", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: "-1", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: ":", type: .colon),
            Token(value: "2", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "5", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: ":", type: .colon),
            Token(value: "-1", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Array indexing and slicing
        "ARRAY_SLICING": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "0", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "s", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: ":", type: .colon),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "s", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "s", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: ":", type: .colon),
            Token(value: "3", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "s", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "s", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: "4", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "s", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "s", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: "-1", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "s", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "s", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: ":", type: .colon),
            Token(value: "2", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "s", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "s", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "strings", type: .identifier),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "5", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: ":", type: .colon),
            Token(value: "-1", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "s", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
        ],

        // Membership operators
        "MEMBERSHIP": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "0", type: .numericLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "b", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "MEMBERSHIP_NEGATION_1": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "0", type: .numericLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "1", type: .numericLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "a", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "b", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "MEMBERSHIP_NEGATION_2": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "0", type: .numericLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "b", type: .stringLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "arr", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Escaped characters
        "ESCAPED_CHARS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "\n", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "\t", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "'", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "\"", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "\\", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "|\n|\t|'|\"|\\|", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
        ],

        // Substring inclusion
        "SUBSTRING_INCLUSION": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "d", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "ab", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "ac", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "abcd", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Filter operator
        "FILTER_OPERATOR": [
            Token(value: "{{", type: .openExpression),
            Token(value: "arr", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "arr", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "arr", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "sort", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{{", type: .openExpression),
            Token(value: "(", type: .openParen),
            Token(value: "arr", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "sort", type: .identifier),
            Token(value: ")", type: .closeParen),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "0", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
        ],
        "FILTER_OPERATOR_2": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "abc", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "aBcD", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "upper", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "aBcD", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "lower", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "test test", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "capitalize", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "test test", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "title", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: " a b ", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "trim", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "  A  B  ", type: .stringLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "trim", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "lower", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "FILTER_OPERATOR_3": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "-1", type: .numericLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "abs", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "|", type: .pipe),
            Token(value: "abs", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "FILTER_OPERATOR_4": [
            Token(value: "{{", type: .openExpression),
            Token(value: "items", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "selectattr", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "key", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
        "FILTER_OPERATOR_5": [
            Token(value: "{{", type: .openExpression),
            Token(value: "messages", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "selectattr", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "role", type: .stringLiteral),
            Token(value: ",", type: .comma),
            Token(value: "equalto", type: .stringLiteral),
            Token(value: ",", type: .comma),
            Token(value: "system", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
        "FILTER_OPERATOR_6": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "(", type: .openParen),
            Token(value: "obj", type: .identifier),
            Token(value: "|", type: .pipe),
            Token(value: "items", type: .identifier),
            Token(value: ")", type: .closeParen),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ":", type: .colon),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Logical operators between non-Booleans
        "BOOLEAN_NUMERICAL": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "and", type: .and),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "and", type: .and),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "0", type: .numericLiteral),
            Token(value: "and", type: .and),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "0", type: .numericLiteral),
            Token(value: "and", type: .and),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "or", type: .or),
            Token(value: "2", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "or", type: .or),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "0", type: .numericLiteral),
            Token(value: "or", type: .or),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "0", type: .numericLiteral),
            Token(value: "or", type: .or),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "BOOLEAN_STRINGS": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "and", type: .and),
            Token(value: "b", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "and", type: .and),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "and", type: .and),
            Token(value: "a", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "and", type: .and),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "or", type: .or),
            Token(value: "b", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "or", type: .or),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "or", type: .or),
            Token(value: "a", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "or", type: .or),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "a", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "not", type: .not),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "BOOLEAN_MIXED": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "1", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "0", type: .numericLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "BOOLEAN_MIXED_2": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "a", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "a", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "and", type: .and),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "and", type: .and),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "", type: .stringLiteral),
            Token(value: "or", type: .or),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "or", type: .or),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "BOOLEAN_MIXED_IF": [
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "a", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "B", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "C", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "a", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "D", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
        ],

        // Tests (is operator)
        "IS_OPERATOR": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "unknown_var", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "defined", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "unknown_var", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "not", type: .not),
            Token(value: "defined", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "known_var", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "defined", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "known_var", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "not", type: .not),
            Token(value: "defined", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "IS_OPERATOR_2": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "is", type: .is),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "is", type: .is),
            Token(value: "not", type: .not),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "is", type: .is),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "is", type: .is),
            Token(value: "not", type: .not),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "is", type: .is),
            Token(value: "boolean", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "boolean", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "IS_OPERATOR_3": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "odd", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "odd", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "even", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "even", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "number", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "number", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "integer", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "integer", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "IS_OPERATOR_4": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "func", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "callable", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "2", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "callable", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "1", type: .numericLiteral),
            Token(value: "is", type: .is),
            Token(value: "iterable", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "hello", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "iterable", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "IS_OPERATOR_5": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "lower", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "lower", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "upper", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "A", type: .stringLiteral),
            Token(value: "is", type: .is),
            Token(value: "upper", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Short-circuit evaluation
        "SHORT_CIRCUIT": [
            Token(value: "{{", type: .openExpression),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "and", type: .and),
            Token(value: "raise_exception", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "This should not be printed", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
        ],
        "SHORT_CIRCUIT_1": [
            Token(value: "{{", type: .openExpression),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "or", type: .or),
            Token(value: "raise_exception", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "This should not be printed", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
        ],

        // Namespaces
        "NAMESPACE": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "ns", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "namespace", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "foo", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "bar", type: .stringLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "foo", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
        "NAMESPACE_1": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "ns", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "namespace", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "default", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "false", type: .booleanLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "default", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
        "NAMESPACE_2": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "ns", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "namespace", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "default", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "false", type: .booleanLiteral),
            Token(value: ",", type: .comma),
            Token(value: "number", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "1", type: .numericLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "default", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "number", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Object operators
        "OBJECT_OPERATORS": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "known", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "obj", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "known", type: .stringLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "obj", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "unknown", type: .stringLiteral),
            Token(value: "in", type: .in),
            Token(value: "obj", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "unknown", type: .stringLiteral),
            Token(value: "not in", type: .notIn),
            Token(value: "obj", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "OBJECT_OPERATORS_1": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "get", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "known", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "get", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "unknown", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "is", type: .is),
            Token(value: "none", type: .nullLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "get", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "unknown", type: .stringLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "is", type: .is),
            Token(value: "defined", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],
        "OBJECT_OPERATORS_2": [
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "x", type: .identifier),
            Token(value: ",", type: .comma),
            Token(value: "y", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "obj", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "items", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: ")", type: .closeParen),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "x", type: .identifier),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: " ", type: .stringLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "y", type: .identifier),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "|", type: .text),
        ],

        // Scope
        "SCOPE": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "ns", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "namespace", type: .identifier),
            Token(value: "(", type: .openParen),
            Token(value: "found", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "false", type: .booleanLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "num", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "nums", type: .identifier),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "num", type: .identifier),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "found=", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "found", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "ns", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "found", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
        "SCOPE_1": [
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "found", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "for", type: .for),
            Token(value: "num", type: .identifier),
            Token(value: "in", type: .in),
            Token(value: "nums", type: .identifier),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "num", type: .identifier),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "found=", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "found", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "endfor", type: .endFor),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "found", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // Undefined
        "UNDEFINED_VARIABLES": [
            Token(value: "{{", type: .openExpression),
            Token(value: "undefined_variable", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
        "UNDEFINED_ACCESS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "object", type: .identifier),
            Token(value: ".", type: .dot),
            Token(value: "undefined_attribute", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // Null
        "NULL_VARIABLE": [
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "not", type: .not),
            Token(value: "null_val", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "defined", type: .identifier),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "set", type: .set),
            Token(value: "null_val", type: .identifier),
            Token(value: "=", type: .equals),
            Token(value: "none", type: .nullLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{%", type: .openStatement),
            Token(value: "if", type: .if),
            Token(value: "null_val", type: .identifier),
            Token(value: "is", type: .is),
            Token(value: "not", type: .not),
            Token(value: "none", type: .nullLiteral),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "fail", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "else", type: .else),
            Token(value: "%}", type: .closeStatement),
            Token(value: "{{", type: .openExpression),
            Token(value: "pass", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "{%", type: .openStatement),
            Token(value: "endif", type: .endIf),
            Token(value: "%}", type: .closeStatement),
        ],

        // Ternary operator
        "TERNARY_OPERATOR": [
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "if", type: .if),
            Token(value: "true", type: .booleanLiteral),
            Token(value: "else", type: .else),
            Token(value: "b", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "if", type: .if),
            Token(value: "false", type: .booleanLiteral),
            Token(value: "else", type: .else),
            Token(value: "b", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "if", type: .if),
            Token(value: "1", type: .numericLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "else", type: .else),
            Token(value: "b", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
            Token(value: "{{", type: .openExpression),
            Token(value: "a", type: .stringLiteral),
            Token(value: "if", type: .if),
            Token(value: "1", type: .numericLiteral),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "1", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "3", type: .numericLiteral),
            Token(value: "or", type: .or),
            Token(value: "1", type: .numericLiteral),
            Token(value: "*", type: .multiplicativeBinaryOperator),
            Token(value: "2", type: .numericLiteral),
            Token(value: "==", type: .comparisonBinaryOperator),
            Token(value: "3", type: .numericLiteral),
            Token(value: "else", type: .else),
            Token(value: "b", type: .stringLiteral),
            Token(value: "}}", type: .closeExpression),
            Token(value: "|", type: .text),
        ],

        // Array literals
        "ARRAY_LITERALS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "true", type: .booleanLiteral),
            Token(value: ",", type: .comma),
            Token(value: "hello", type: .stringLiteral),
            Token(value: ",", type: .comma),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "2", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "3", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "4", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: ",", type: .comma),
            Token(value: "var", type: .identifier),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // Tuple literals
        "TUPLE_LITERALS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "(", type: .openParen),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "(", type: .openParen),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "2", type: .numericLiteral),
            Token(value: ")", type: .closeParen),
            Token(value: ")", type: .closeParen),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],

        // Object literals
        "OBJECT_LITERALS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "{", type: .openCurlyBracket),
            Token(value: "key", type: .stringLiteral),
            Token(value: ":", type: .colon),
            Token(value: "value", type: .stringLiteral),
            Token(value: ",", type: .comma),
            Token(value: "key", type: .identifier),
            Token(value: ":", type: .colon),
            Token(value: "value2", type: .stringLiteral),
            Token(value: ",", type: .comma),
            Token(value: "key3", type: .stringLiteral),
            Token(value: ":", type: .colon),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "{", type: .openCurlyBracket),
            Token(value: "foo", type: .stringLiteral),
            Token(value: ":", type: .colon),
            Token(value: "bar", type: .stringLiteral),
            Token(value: "}", type: .closeCurlyBracket),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}", type: .closeCurlyBracket),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "key", type: .stringLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "}}", type: .closeExpression),
        ],

        // Array operators
        "ARRAY_OPERATORS": [
            Token(value: "{{", type: .openExpression),
            Token(value: "(", type: .openParen),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "1", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "2", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "3", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: "+", type: .additiveBinaryOperator),
            Token(value: "[", type: .openSquareBracket),
            Token(value: "4", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "5", type: .numericLiteral),
            Token(value: ",", type: .comma),
            Token(value: "6", type: .numericLiteral),
            Token(value: "]", type: .closeSquareBracket),
            Token(value: ")", type: .closeParen),
            Token(value: "|", type: .pipe),
            Token(value: "length", type: .identifier),
            Token(value: "}}", type: .closeExpression),
        ],
    ]

    func testTokenize() throws {
        for (name, text) in testStrings {
            let tokens = try tokenize(text)
            XCTAssertNotNil(testParsed[name], "Test case \(name) not found")
            XCTAssertEqual(tokens, testParsed[name], "Test case \(name) failed")
        }
    }
}
