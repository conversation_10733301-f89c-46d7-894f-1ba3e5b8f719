{"bert-base-uncased": [{"input": "hello world", "encoded": {"input_ids": [101, 7592, 2088, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] hello world [SEP]", "decoded_without_special": "hello world"}, {"input": "Hello World", "encoded": {"input_ids": [101, 7592, 2088, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] hello world [SEP]", "decoded_without_special": "hello world"}, {"input": "How are you doing?", "encoded": {"input_ids": [101, 2129, 2024, 2017, 2725, 1029, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] how are you doing? [SEP]", "decoded_without_special": "how are you doing?"}, {"input": "You should've done this", "encoded": {"input_ids": [101, 2017, 2323, 1005, 2310, 2589, 2023, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] you should've done this [SEP]", "decoded_without_special": "you should've done this"}, {"input": "A\n'll !!to?'d''d of, can't.", "encoded": {"input_ids": [101, 1037, 1005, 2222, 999, 999, 2000, 1029, 1005, 1040, 1005, 1005, 1040, 1997, 1010, 2064, 1005, 1056, 1012, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] a'll!! to?'d'' d of, can't. [SEP]", "decoded_without_special": "a'll!! to?'d'' d of, can't."}, {"input": "def main():\n\tpass", "encoded": {"input_ids": [101, 13366, 2364, 1006, 1007, 1024, 3413, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] def main ( ) : pass [SEP]", "decoded_without_special": "def main ( ) : pass"}, {"input": "This\n\nis\na\ntest.", "encoded": {"input_ids": [101, 2023, 2003, 1037, 3231, 1012, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] this is a test. [SEP]", "decoded_without_special": "this is a test."}, {"input": "let a = obj.toString();\ntoString();", "encoded": {"input_ids": [101, 2292, 1037, 1027, 27885, 3501, 1012, 2000, 3367, 4892, 1006, 1007, 1025, 2000, 3367, 4892, 1006, 1007, 1025, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] let a = obj. tostring ( ) ; tostring ( ) ; [SEP]", "decoded_without_special": "let a = obj. tostring ( ) ; tostring ( ) ;"}, {"input": "Hi  Hello", "encoded": {"input_ids": [101, 7632, 7592, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] hi hello [SEP]", "decoded_without_special": "hi hello"}, {"input": "trailing space   ", "encoded": {"input_ids": [101, 12542, 2686, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] trailing space [SEP]", "decoded_without_special": "trailing space"}, {"input": "   leading space", "encoded": {"input_ids": [101, 2877, 2686, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] leading space [SEP]", "decoded_without_special": "leading space"}, {"input": "生活的真谛是", "encoded": {"input_ids": [101, 1910, 100, 1916, 1921, 100, 100, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] 生 [UNK] 的 真 [UNK] [UNK] [SEP]", "decoded_without_special": "生 的 真"}, {"input": "The company was founded in 2016.", "encoded": {"input_ids": [101, 1996, 2194, 2001, 2631, 1999, 2355, 1012, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] the company was founded in 2016. [SEP]", "decoded_without_special": "the company was founded in 2016."}, {"input": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "encoded": {"input_ids": [101, 3231, 1002, 1015, 1054, 2475, 1001, 1017, 1574, 2549, 27813, 1071, 2575, 100, 1576, 2620, 1575, 2683, 3231, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] test $ 1 r2 # 3 €4 £5 ¥6 [UNK] ₹8 ₱9 test [SEP]", "decoded_without_special": "test $ 1 r2 # 3 €4 £5 ¥6 ₹8 ₱9 test"}, {"input": "I bought an apple for $1.00 at the store.", "encoded": {"input_ids": [101, 1045, 4149, 2019, 6207, 2005, 1002, 1015, 1012, 4002, 2012, 1996, 3573, 1012, 102], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] i bought an apple for $ 1. 00 at the store. [SEP]", "decoded_without_special": "i bought an apple for $ 1. 00 at the store."}, {"input": "you…  ", "encoded": {"input_ids": [101, 2017, 1529, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] you … [SEP]", "decoded_without_special": "you …"}, {"input": "you…  ", "encoded": {"input_ids": [101, 2017, 1529, 102], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "[CLS] you … [SEP]", "decoded_without_special": "you …"}, {"input": "you…  you…  ", "encoded": {"input_ids": [101, 2017, 1529, 2017, 1529, 102], "token_type_ids": [0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "[CLS] you … you … [SEP]", "decoded_without_special": "you … you …"}], "distilgpt2": [{"input": "hello world", "encoded": {"input_ids": [31373, 995], "attention_mask": [1, 1]}, "decoded_with_special": "hello world", "decoded_without_special": "hello world"}, {"input": "Hello World", "encoded": {"input_ids": [15496, 2159], "attention_mask": [1, 1]}, "decoded_with_special": "Hello World", "decoded_without_special": "Hello World"}, {"input": "How are you doing?", "encoded": {"input_ids": [2437, 389, 345, 1804, 30], "attention_mask": [1, 1, 1, 1, 1]}, "decoded_with_special": "How are you doing?", "decoded_without_special": "How are you doing?"}, {"input": "You should've done this", "encoded": {"input_ids": [1639, 815, 1053, 1760, 428], "attention_mask": [1, 1, 1, 1, 1]}, "decoded_with_special": "You should've done this", "decoded_without_special": "You should've done this"}, {"input": "A\n'll !!to?'d''d of, can't.", "encoded": {"input_ids": [32, 198, 1183, 37867, 1462, 8348, 67, 7061, 67, 286, 11, 460, 470, 13], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "A\n'll!!to?'d''d of, can't.", "decoded_without_special": "A\n'll!!to?'d''d of, can't."}, {"input": "def main():\n\tpass", "encoded": {"input_ids": [4299, 1388, 33529, 198, 197, 6603], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "def main():\n\tpass", "decoded_without_special": "def main():\n\tpass"}, {"input": "This\n\nis\na\ntest.", "encoded": {"input_ids": [1212, 198, 198, 271, 198, 64, 198, 9288, 13], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "This\n\nis\na\ntest.", "decoded_without_special": "This\n\nis\na\ntest."}, {"input": "let a = obj.toString();\ntoString();", "encoded": {"input_ids": [1616, 257, 796, 26181, 13, 1462, 10100, 9783, 198, 1462, 10100, 9783], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "let a = obj.toString();\ntoString();", "decoded_without_special": "let a = obj.toString();\ntoString();"}, {"input": "Hi  Hello", "encoded": {"input_ids": [17250, 220, 18435], "attention_mask": [1, 1, 1]}, "decoded_with_special": "Hi  Hello", "decoded_without_special": "Hi  Hello"}, {"input": "trailing space   ", "encoded": {"input_ids": [9535, 4386, 2272, 220, 220, 220], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "trailing space   ", "decoded_without_special": "trailing space   "}, {"input": "   leading space", "encoded": {"input_ids": [220, 220, 3756, 2272], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "   leading space", "decoded_without_special": "   leading space"}, {"input": "生活的真谛是", "encoded": {"input_ids": [37955, 162, 112, 119, 21410, 40367, 253, 164, 108, 249, 42468], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "生活的真谛是", "decoded_without_special": "生活的真谛是"}, {"input": "The company was founded in 2016.", "encoded": {"input_ids": [464, 1664, 373, 9393, 287, 1584, 13], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "The company was founded in 2016.", "decoded_without_special": "The company was founded in 2016."}, {"input": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "encoded": {"input_ids": [9288, 720, 16, 371, 17, 1303, 18, 10432, 19, 4248, 20, 38221, 21, 2343, 224, 96, 22, 2343, 224, 117, 23, 2343, 224, 109, 24, 1332], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "decoded_without_special": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test"}, {"input": "I bought an apple for $1.00 at the store.", "encoded": {"input_ids": [40, 5839, 281, 17180, 329, 720, 16, 13, 405, 379, 262, 3650, 13], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "I bought an apple for $1.00 at the store.", "decoded_without_special": "I bought an apple for $1.00 at the store."}, {"input": "you…  ", "encoded": {"input_ids": [5832, 1399, 220, 220], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "you…  ", "decoded_without_special": "you…  "}, {"input": "you…  ", "encoded": {"input_ids": [5832, 1399, 4603], "attention_mask": [1, 1, 1]}, "decoded_with_special": "you…  ", "decoded_without_special": "you…  "}, {"input": "you…  you…  ", "encoded": {"input_ids": [5832, 1399, 1849, 1849, 5832, 1399, 4603], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "you…  you…  ", "decoded_without_special": "you…  you…  "}], "coreml-projects/Llama-2-7b-chat-coreml": [{"input": "hello world", "encoded": {"input_ids": [1, 22172, 3186], "attention_mask": [1, 1, 1]}, "decoded_with_special": "<s> hello world", "decoded_without_special": "hello world"}, {"input": "Hello World", "encoded": {"input_ids": [1, 15043, 2787], "attention_mask": [1, 1, 1]}, "decoded_with_special": "<s> Hello World", "decoded_without_special": "Hello World"}, {"input": "How are you doing?", "encoded": {"input_ids": [1, 1128, 526, 366, 2599, 29973], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> How are you doing?", "decoded_without_special": "How are you doing?"}, {"input": "You should've done this", "encoded": {"input_ids": [1, 887, 881, 29915, 345, 2309, 445], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> You should've done this", "decoded_without_special": "You should've done this"}, {"input": "A\n'll !!to?'d''d of, can't.", "encoded": {"input_ids": [1, 319, 13, 29915, 645, 21443, 517, 17901, 29881, 4907, 29881, 310, 29892, 508, 29915, 29873, 29889], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> A\n'll !!to?'d''d of, can't.", "decoded_without_special": "A\n'll !!to?'d''d of, can't."}, {"input": "def main():\n\tpass", "encoded": {"input_ids": [1, 822, 1667, 7295, 13, 12, 3364], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> def main():\n\tpass", "decoded_without_special": "def main():\n\tpass"}, {"input": "This\n\nis\na\ntest.", "encoded": {"input_ids": [1, 910, 13, 13, 275, 13, 29874, 13, 1688, 29889], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> This\n\nis\na\ntest.", "decoded_without_special": "This\n\nis\na\ntest."}, {"input": "let a = obj.toString();\ntoString();", "encoded": {"input_ids": [1, 1235, 263, 353, 5446, 29889, 7711, 890, 13, 7711, 890], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> let a = obj.toString();\ntoString();", "decoded_without_special": "let a = obj.toString();\ntoString();"}, {"input": "Hi  Hello", "encoded": {"input_ids": [1, 6324, 29871, 15043], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "<s> Hi  Hello", "decoded_without_special": "Hi  Hello"}, {"input": "trailing space   ", "encoded": {"input_ids": [1, 25053, 2913, 1678], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "<s> trailing space   ", "decoded_without_special": "trailing space   "}, {"input": "   leading space", "encoded": {"input_ids": [1, 1678, 8236, 2913], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "<s>    leading space", "decoded_without_special": "   leading space"}, {"input": "生活的真谛是", "encoded": {"input_ids": [1, 29871, 30486, 31704, 30210, 30848, 235, 179, 158, 30392], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> 生活的真谛是", "decoded_without_special": "生活的真谛是"}, {"input": "The company was founded in 2016.", "encoded": {"input_ids": [1, 450, 5001, 471, 11091, 297, 29871, 29906, 29900, 29896, 29953, 29889], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> The company was founded in 2016.", "decoded_without_special": "The company was founded in 2016."}, {"input": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "encoded": {"input_ids": [1, 1243, 395, 29896, 390, 29906, 396, 29941, 25540, 29946, 15151, 29945, 29871, 30563, 29953, 29871, 229, 133, 166, 29955, 29871, 30620, 29947, 29871, 229, 133, 180, 29929, 1243], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "decoded_without_special": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test"}, {"input": "I bought an apple for $1.00 at the store.", "encoded": {"input_ids": [1, 306, 18093, 385, 26163, 363, 395, 29896, 29889, 29900, 29900, 472, 278, 3787, 29889], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> I bought an apple for $1.00 at the store.", "decoded_without_special": "I bought an apple for $1.00 at the store."}, {"input": "you…  ", "encoded": {"input_ids": [1, 366, 30098, 259], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "<s> you…  ", "decoded_without_special": "you…  "}, {"input": "you…  ", "encoded": {"input_ids": [1, 366, 30098, 8655], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "<s> you…  ", "decoded_without_special": "you…  "}, {"input": "you…  you…  ", "encoded": {"input_ids": [1, 366, 30098, 8655, 6293, 30098, 8655], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "<s> you…  you…  ", "decoded_without_special": "you…  you…  "}], "tiiuae/falcon-7b": [{"input": "hello world", "encoded": {"input_ids": [30835, 1079], "token_type_ids": [0, 0], "attention_mask": [1, 1]}, "decoded_with_special": "hello world", "decoded_without_special": "hello world"}, {"input": "Hello World", "encoded": {"input_ids": [9856, 2889], "token_type_ids": [0, 0], "attention_mask": [1, 1]}, "decoded_with_special": "Hello World", "decoded_without_special": "Hello World"}, {"input": "How are you doing?", "encoded": {"input_ids": [1830, 362, 299, 1836, 42], "token_type_ids": [0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1]}, "decoded_with_special": "How are you doing?", "decoded_without_special": "How are you doing?"}, {"input": "You should've done this", "encoded": {"input_ids": [1357, 808, 18, 298, 1782, 414], "token_type_ids": [0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "You should've done this", "decoded_without_special": "You should've done this"}, {"input": "A\n'll !!to?'d''d of, can't.", "encoded": {"input_ids": [44, 193, 18, 567, 204, 1409, 534, 12493, 79, 7544, 79, 275, 23, 418, 18, 95, 25], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "A\n'll!!to?'d''d of, can't.", "decoded_without_special": "A\n'll!!to?'d''d of, can't."}, {"input": "def main():\n\tpass", "encoded": {"input_ids": [3071, 1316, 13160, 193, 192, 5412], "token_type_ids": [0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "def main():\n\tpass", "decoded_without_special": "def main():\n\tpass"}, {"input": "This\n\nis\na\ntest.", "encoded": {"input_ids": [1182, 193, 193, 259, 193, 76, 193, 4780, 25], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "This\n\nis\na\ntest.", "decoded_without_special": "This\n\nis\na\ntest."}, {"input": "let a = obj.toString();\ntoString();", "encoded": {"input_ids": [1025, 241, 204, 40, 13756, 25, 19409, 2032, 193, 19409, 2032], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "let a = obj.toString();\ntoString();", "decoded_without_special": "let a = obj.toString();\ntoString();"}, {"input": "Hi  Hello", "encoded": {"input_ids": [5516, 204, 23090], "token_type_ids": [0, 0, 0], "attention_mask": [1, 1, 1]}, "decoded_with_special": "Hi  Hello", "decoded_without_special": "Hi  Hello"}, {"input": "trailing space   ", "encoded": {"input_ids": [9172, 4447, 2151, 466], "token_type_ids": [0, 0, 0, 0], "attention_mask": [1, 1, 1, 1]}, "decoded_with_special": "trailing space   ", "decoded_without_special": "trailing space   "}, {"input": "   leading space", "encoded": {"input_ids": [258, 3736, 2151], "token_type_ids": [0, 0, 0], "attention_mask": [1, 1, 1]}, "decoded_with_special": "   leading space", "decoded_without_special": "   leading space"}, {"input": "生活的真谛是", "encoded": {"input_ids": [32725, 1105, 15498, 8061, 233, 2364], "token_type_ids": [0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1]}, "decoded_with_special": "生活的真谛是", "decoded_without_special": "生活的真谛是"}, {"input": "The company was founded in 2016.", "encoded": {"input_ids": [487, 1438, 398, 9923, 272, 204, 626, 33, 25], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "The company was founded in 2016.", "decoded_without_special": "The company was founded in 2016."}, {"input": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "encoded": {"input_ids": [4780, 204, 15, 28, 382, 29, 204, 14, 30, 6471, 31, 5131, 32, 3068, 110, 33, 25631, 108, 34, 25631, 129, 35, 25631, 121, 36, 1318], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test", "decoded_without_special": "test $1 R2 #3 €4 £5 ¥6 ₣7 ₹8 ₱9 test"}, {"input": "I bought an apple for $1.00 at the store.", "encoded": {"input_ids": [52, 5659, 267, 12381, 312, 204, 15, 28, 25, 527, 388, 248, 2946, 25], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "I bought an apple for $1.00 at the store.", "decoded_without_special": "I bought an apple for $1.00 at the store."}, {"input": "you…  ", "encoded": {"input_ids": [5667, 898, 258], "token_type_ids": [0, 0, 0], "attention_mask": [1, 1, 1]}, "decoded_with_special": "you…  ", "decoded_without_special": "you…  "}, {"input": "you…  ", "encoded": {"input_ids": [5667, 898, 60482], "token_type_ids": [0, 0, 0], "attention_mask": [1, 1, 1]}, "decoded_with_special": "you…  ", "decoded_without_special": "you…  "}, {"input": "you…  you…  ", "encoded": {"input_ids": [5667, 898, 4381, 4381, 5667, 898, 60482], "token_type_ids": [0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "you…  you…  ", "decoded_without_special": "you…  you…  "}, {"input": "12 and 123 and 1234", "encoded": {"input_ids": [928, 273, 204, 10963, 273, 204, 10963, 31], "token_type_ids": [0, 0, 0, 0, 0, 0, 0, 0], "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]}, "decoded_with_special": "12 and 123 and 1234", "decoded_without_special": "12 and 123 and 1234"}]}