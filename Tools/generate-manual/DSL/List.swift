//===----------------------------------------------------------*- swift -*-===//
//
// This source file is part of the Swift Argument Parser open source project
//
// Copyright (c) 2021 Apple Inc. and the Swift project authors
// Licensed under Apache License v2.0 with Runtime Library Exception
//
// See https://swift.org/LICENSE.txt for license information
//
//===----------------------------------------------------------------------===//

struct List: MDocComponent {
  var content: MDocComponent

  init(@MDocBuilder content: () -> MDocComponent) {
    self.content = content()
  }

  var body: MDocComponent {
    if !content.ast.isEmpty {
      MDocMacro.BeginList(style: .tag, width: 6)
      content
      MDocMacro.EndList()
    }
  }
}
