name: Development Tests

on:
  pull_request:
  pull_request_review:
    types: [submitted]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  build-and-test:
    name: "Build and Test"
    uses: ./.github/workflows/unit-tests.yml
    with:
      ios-version: "18.2"
      ios-device: "iPhone 16"
      macos-runner: "macos-15"

  check-approvals:
    runs-on: ubuntu-latest
    outputs:
      reviews: ${{ steps.reviews.outputs.state }}
    permissions:
      pull-requests: read
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Check Approvals
        id: reviews
        env:
          GH_TOKEN: ${{ github.token }}
          pr: ${{ github.event.pull_request.number }}
        run: |
          echo "Checking PR approval for: $pr"
          state=$(gh pr view $pr --json reviewDecision --jq '.reviewDecision')
          echo "Review decision state: $state"
          echo "state=$state" >> "$GITHUB_OUTPUT"

  pre-merge-tests:
    name: "Pre-merge Tests"
    needs: [check-approvals]
    if: needs.check-approvals.outputs.reviews == 'APPROVED' || github.event_name == 'workflow_dispatch'
    strategy:
      matrix:
        include:
          - os: macos-13-xlarge
            ios-version: "17.2"
            ios-device: "iPhone 14"
            xcode-version: "15.2"
          - os: macos-14
            ios-version: "17.2"
            ios-device: "iPhone 15"
            xcode-version: "15.2"
    uses: ./.github/workflows/unit-tests.yml
    with:
      macos-runner: ${{ matrix.os }}
      ios-version: ${{ matrix.ios-version }}
      ios-device: ${{ matrix.ios-device }}
      xcode-version: ${{ matrix.xcode-version }}
