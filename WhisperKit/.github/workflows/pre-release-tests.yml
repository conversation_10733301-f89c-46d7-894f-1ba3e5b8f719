name: Pre-Release Tests

on:
  push:
    branches: ["main"]
  workflow_dispatch:

jobs:
  build-and-test-all-platforms:
    name: "Build and Test All Platforms"
    strategy:
      matrix:
        include:
          - os: macos-13-xlarge
            ios-version: "17.2" # TODO: Download older simulators for macOS 13
            ios-device: "iPhone 14"
            xcode-version: "15.2"
          - os: macos-14
            ios-version: "17.2"
            ios-device: "iPhone 15"
            xcode-version: "15.2"
          - os: macos-15
            ios-version: "18.2" # Latest available version
            ios-device: "iPhone 16"
            xcode-version: "latest-stable"
    uses: ./.github/workflows/unit-tests.yml
    with:
      macos-runner: ${{ matrix.os }}
      ios-version: ${{ matrix.ios-version }}
      ios-device: ${{ matrix.ios-device }}
      xcode-version: ${{ matrix.xcode-version }}