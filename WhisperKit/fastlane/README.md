fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios list_devices

```sh
[bundle exec] fastlane ios list_devices
```

List all connected devices

### ios benchmark

```sh
[bundle exec] fastlane ios benchmark
```

Benchmark devices with options

### ios extract_results

```sh
[bundle exec] fastlane ios extract_results
```

Extract benchmark results

### ios upload_results

```sh
[bundle exec] fastlane ios upload_results
```

Upload benchmark results

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
