#  Xcode build files

The project file here is used to build a variant of this package with Xcode. The project file is a regular Xcode project that builds the code base using the COLLECTIONS_SINGLE_MODULE configuration, producing a single framework bundle. Builds settings are entirely configured via the provided xcconfig files.

Beware! The contents of this directory are not source stable. They are provided as is, with no compatibility promises across package releases. Future versions of this package can arbitrarily change these files or remove them, without any advance notice. (This can include patch releases.)
