import Foundation
import WhisperKit
import CoreAudio

// MARK: - CoreAudio Utilities
enum AudioError: Error {
    case deviceCreationFailed
    case noInputDevice
    case tapCreationFailed
    case recordingError(String)
    case permissionDenied
}

// MARK: - Audio Recording Manager
class AudioRecorder {
    // CoreAudio identifiers
    private var processTapID: AudioObjectID?
    private var aggregateDeviceID: AudioObjectID?
    private var ioProcID: AudioDeviceIOProcID?
    
    // Recording state
    private var isRecording = false
    private var recordingURL: URL?
    private var audioFile: AVAudioFile?
    private var audioFormat: AVAudioFormat?
    
    // Configuration
    private let sampleRate: Double = 16000 // WhisperKit works best with 16kHz audio
    
    // Cleanup function to ensure all resources are properly released
    deinit {
        cleanup()
    }
    
    // Set up recording from microphone
    func setupMicrophoneRecording() throws {
        // Request permission first - in a real app, this should be handled properly
        // For this example, we'll assume permission is granted
        
        // Find default input device
        var defaultInputDeviceID: AudioDeviceID = 0
        var propertySize = UInt32(MemoryLayout<AudioDeviceID>.size)
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioHardwarePropertyDefaultInputDevice,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            AudioObjectID(kAudioObjectSystemObject),
            &propertyAddress,
            0,
            nil,
            &propertySize,
            &defaultInputDeviceID
        )
        
        if status != noErr {
            throw AudioError.noInputDevice
        }
        
        // Create description for the tap
        let tapDescription = CATapDescription()
        tapDescription.objectID = defaultInputDeviceID
        
        // Get UUID for the tap
        let tapUUID = tapDescription.uuid
        
        // Create the process tap
        var processObjectID: AudioObjectID = 0
        var result = AudioHardwareCreateProcessTap(tapDescription, &processObjectID)
        if result != noErr {
            throw AudioError.tapCreationFailed
        }
        processTapID = processObjectID
        
        // Create aggregate device with the tap
        var aggregateDeviceID: AudioDeviceID = 0
        let tapUUIDString = tapUUID.uuidString
        
        // Prepare the aggregate device description
        var aggregateDeviceDescription: [String: Any] = [
            kAudioAggregateDeviceNameKey: "Transcriptor9000 Recording Device",
            kAudioAggregateDeviceIsPrivateKey: true,
            kAudioAggregateDeviceIsStackedKey: false,
            kAudioAggregateDeviceTapListKey: [
                [kAudioSubTapUIDKey: tapUUIDString]
            ]
        ]
        
        // Create the aggregate device
        result = AudioHardwareCreateAggregateDevice(&aggregateDeviceDescription, &aggregateDeviceID)
        if result != noErr {
            AudioHardwareDestroyProcessTap(processObjectID)
            throw AudioError.deviceCreationFailed
        }
        self.aggregateDeviceID = aggregateDeviceID
        
        // Get the audio format from the process tap
        var formatSize = UInt32(MemoryLayout<AudioStreamBasicDescription>.size)
        var asbd = AudioStreamBasicDescription()
        propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioTapPropertyFormat,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        result = AudioObjectGetPropertyData(
            processObjectID,
            &propertyAddress,
            0,
            nil,
            &formatSize,
            &asbd
        )
        
        if result != noErr {
            cleanup()
            throw AudioError.recordingError("Failed to get audio format")
        }
        
        // Create AVAudioFormat from the AudioStreamBasicDescription
        audioFormat = AVAudioFormat(
            commonFormat: .pcmFormatFloat32,
            sampleRate: Double(asbd.mSampleRate),
            channels: UInt32(asbd.mChannelsPerFrame),
            interleaved: false
        )
        
        print("Audio format set up: \(String(describing: audioFormat))")
    }
    
    // Temporary file URL for recording
    private var tempFileURL: URL {
        let tempDir = FileManager.default.temporaryDirectory
        return tempDir.appendingPathComponent("recording_\(Date().timeIntervalSince1970).wav")
    }
    
    // Start recording
    func startRecording() throws {
        guard let aggregateDeviceID = aggregateDeviceID, let audioFormat = audioFormat else {
            throw AudioError.recordingError("Setup not completed")
        }
        
        if isRecording {
            return
        }
        
        // Create the file for recording
        recordingURL = tempFileURL
        audioFile = try AVAudioFile(
            forWriting: recordingURL!,
            settings: audioFormat.settings
        )
        
        // Create IO proc for the aggregate device
        var procID: AudioDeviceIOProcID?
        let result = AudioDeviceCreateIOProcIDWithBlock(
            &procID,
            aggregateDeviceID,
            nil
        ) { [weak self] (_, _, _, bufferList) -> OSStatus in
            guard let self = self, let audioFile = self.audioFile, let format = self.audioFormat else {
                return noErr
            }
            
            // Create a buffer from the audio data
            let buffer = AVAudioPCMBuffer.bufferListNoCopy(
                format: format,
                bufferList: bufferList,
                deallocator: nil
            )
            
            do {
                // Write the buffer to the file
                try audioFile.write(from: buffer!)
            } catch {
                print("Error writing to audio file: \(error)")
            }
            
            return noErr
        }
        
        if result != noErr {
            cleanup()
            throw AudioError.recordingError("Failed to create IO proc")
        }
        
        ioProcID = procID
        
        // Start the device
        AudioDeviceStart(aggregateDeviceID, procID)
        isRecording = true
        
        print("Recording started at \(recordingURL?.path ?? "unknown path")")
    }
    
    // Stop recording
    func stopRecording() -> URL? {
        guard isRecording, let aggregateDeviceID = aggregateDeviceID, let ioProcID = ioProcID else {
            return nil
        }
        
        // Stop the device
        AudioDeviceStop(aggregateDeviceID, ioProcID)
        isRecording = false
        
        let recordedFileURL = recordingURL
        print("Recording stopped. File saved at \(recordedFileURL?.path ?? "unknown path")")
        
        return recordedFileURL
    }
    
    // Clean up resources
    func cleanup() {
        if let aggregateDeviceID = aggregateDeviceID, let ioProcID = ioProcID {
            if isRecording {
                AudioDeviceStop(aggregateDeviceID, ioProcID)
                isRecording = false
            }
            
            AudioDeviceDestroyIOProcID(aggregateDeviceID, ioProcID)
            self.ioProcID = nil
        }
        
        if let aggregateDeviceID = aggregateDeviceID {
            AudioHardwareDestroyAggregateDevice(aggregateDeviceID)
            self.aggregateDeviceID = nil
        }
        
        if let processTapID = processTapID {
            AudioHardwareDestroyProcessTap(processTapID)
            self.processTapID = nil
        }
        
        audioFile = nil
    }
}

// MARK: - Transcription Manager
class TranscriptionManager {
    private var whisperKit: WhisperKit?
    
    // Initialize WhisperKit
    func setup() async throws {
        whisperKit = WhisperKit(modelFolder: "WhisperKit", modelType: .openAI(.tiny))
        try await whisperKit?.loadModel()
        print("WhisperKit model loaded successfully")
    }
    
    // Transcribe audio file
    func transcribe(audioURL: URL) async throws -> String {
        guard let whisperKit = whisperKit else {
            throw NSError(domain: "TranscriptionError", code: 1, userInfo: [NSLocalizedDescriptionKey: "WhisperKit not initialized"])
        }
        
        // Perform transcription
        let result = try await whisperKit.transcribe(audioURL: audioURL)
        
        // Extract transcribed text
        return result.text
    }
}

// MARK: - Main App
@main
struct Transcriptor9000 {
    static func main() async throws {
        print("Transcriptor9000 - Audio Transcription Tool")
        print("------------------------------------------")
        
        // Create instances
        let audioRecorder = AudioRecorder()
        let transcriptionManager = TranscriptionManager()
        
        // Setup components
        print("Setting up audio recording...")
        try audioRecorder.setupMicrophoneRecording()
        
        print("Setting up WhisperKit for transcription...")
        try await transcriptionManager.setup()
        
        // Command loop
        var running = true
        
        while running {
            print("\nCommands:")
            print("1: Start recording")
            print("2: Stop recording and transcribe")
            print("q: Quit")
            print("> ", terminator: "")
            
            guard let input = readLine() else { continue }
            
            switch input {
            case "1":
                do {
                    try audioRecorder.startRecording()
                    print("Recording... (enter '2' to stop and transcribe)")
                } catch {
                    print("Error starting recording: \(error)")
                }
                
            case "2":
                guard let audioURL = audioRecorder.stopRecording() else {
                    print("No active recording to stop")
                    continue
                }
                
                print("Transcribing audio...")
                do {
                    let transcription = try await transcriptionManager.transcribe(audioURL: audioURL)
                    print("\n--- Transcription Result ---")
                    print(transcription)
                    print("---------------------------")
                } catch {
                    print("Transcription error: \(error)")
                }
                
            case "q", "Q":
                print("Exiting Transcriptor9000")
                running = false
                
            default:
                print("Unknown command: \(input)")
            }
        }
        
        // Cleanup
        audioRecorder.cleanup()
    }
}
